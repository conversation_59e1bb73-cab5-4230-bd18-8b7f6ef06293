"""Run all tests for the trading system."""

import sys
import asyncio
import subprocess
from pathlib import Path

# Add current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from utils.logger import setup_logging, get_logger

# Setup logging
setup_logging()
logger = get_logger(__name__)


def run_pytest():
    """Run pytest tests."""
    logger.info("Running pytest tests")
    
    try:
        result = subprocess.run([
            sys.executable, "-m", "pytest", 
            "tests/", 
            "-v", 
            "--tb=short"
        ], capture_output=True, text=True)
        
        print("PYTEST OUTPUT:")
        print("=" * 50)
        print(result.stdout)
        if result.stderr:
            print("PYTEST ERRORS:")
            print(result.stderr)
        
        return result.returncode == 0
        
    except Exception as e:
        logger.error("Failed to run pytest", error=str(e))
        return False


async def run_connection_test():
    """Run connection test."""
    logger.info("Running connection test")
    
    try:
        # Import and run the connection test
        from scripts.test_connection import main as test_main
        await test_main()
        return True
        
    except Exception as e:
        logger.error("Connection test failed", error=str(e))
        return False


def main():
    """Main test runner."""
    print("TradingView to MT5 Trading System - Test Suite")
    print("=" * 60)
    
    # Run pytest tests
    pytest_success = run_pytest()
    
    # Run connection tests
    print("\n" + "=" * 60)
    print("Running Connection Tests")
    print("=" * 60)
    
    connection_success = asyncio.run(run_connection_test())
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    print(f"Pytest Tests: {'✅ PASSED' if pytest_success else '❌ FAILED'}")
    print(f"Connection Tests: {'✅ PASSED' if connection_success else '❌ FAILED'}")
    
    if pytest_success and connection_success:
        print("\n🎉 ALL TESTS PASSED! System is ready for production.")
        print("\nNext steps:")
        print("1. Configure your .env file with real MT5 credentials")
        print("2. Start the system: python main.py")
        print("3. Test with TradingView alerts")
    else:
        print("\n❌ Some tests failed. Please check the output above.")
        print("Make sure:")
        print("- MetaTrader 5 is installed and running")
        print("- Your .env file is properly configured")
        print("- Your MT5 account allows automated trading")


if __name__ == "__main__":
    main()
