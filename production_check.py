"""Production readiness checker and monitoring."""

import asyncio
import json
import time
import requests
from datetime import datetime, timedelta
from typing import Dict, List, Any
import sys
from pathlib import Path
import psutil
import os

# Add current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from utils.logger import setup_logging, get_logger
from config import config

# Setup logging
setup_logging()
logger = get_logger(__name__)


class ProductionChecker:
    """Production readiness and monitoring checker."""
    
    def __init__(self, base_url: str = "http://localhost:7000"):
        self.base_url = base_url
        self.checks = []
    
    def add_check(self, name: str, passed: bool, details: str = "", critical: bool = True):
        """Add a check result."""
        status = "✅ PASS" if passed else ("🔴 CRITICAL" if critical else "🟡 WARNING")
        check = {
            "name": name,
            "passed": passed,
            "critical": critical,
            "details": details,
            "timestamp": datetime.now().isoformat()
        }
        self.checks.append(check)
        print(f"{status} - {name}")
        if details:
            print(f"    {details}")
    
    def check_system_resources(self):
        """Check system resources."""
        print("\n" + "="*60)
        print("🖥️  SYSTEM RESOURCES CHECK")
        print("="*60)
        
        # CPU usage
        cpu_percent = psutil.cpu_percent(interval=1)
        self.add_check(
            "CPU Usage",
            cpu_percent < 80,
            f"Current: {cpu_percent}% (should be < 80%)",
            critical=False
        )
        
        # Memory usage
        memory = psutil.virtual_memory()
        memory_percent = memory.percent
        self.add_check(
            "Memory Usage",
            memory_percent < 85,
            f"Current: {memory_percent}% (should be < 85%)",
            critical=False
        )
        
        # Disk space
        disk = psutil.disk_usage('.')
        disk_percent = (disk.used / disk.total) * 100
        self.add_check(
            "Disk Space",
            disk_percent < 90,
            f"Current: {disk_percent:.1f}% used (should be < 90%)",
            critical=True
        )
        
        # Available memory
        available_gb = memory.available / (1024**3)
        self.add_check(
            "Available Memory",
            available_gb > 1,
            f"Available: {available_gb:.1f} GB (should be > 1 GB)",
            critical=True
        )
    
    def check_network_connectivity(self):
        """Check network connectivity."""
        print("\n" + "="*60)
        print("🌐 NETWORK CONNECTIVITY CHECK")
        print("="*60)
        
        # Check server response
        try:
            response = requests.get(f"{self.base_url}/health", timeout=5)
            self.add_check(
                "Server Response",
                response.status_code == 200,
                f"Status: {response.status_code}",
                critical=True
            )
            
            if response.status_code == 200:
                health_data = response.json()
                mt5_connected = health_data.get("mt5_connected", False)
                self.add_check(
                    "MT5 Connection",
                    mt5_connected,
                    f"Connected: {mt5_connected}",
                    critical=True
                )
        except Exception as e:
            self.add_check(
                "Server Response",
                False,
                f"Error: {str(e)}",
                critical=True
            )
        
        # Check external connectivity (optional)
        try:
            response = requests.get("https://httpbin.org/status/200", timeout=5)
            self.add_check(
                "External Internet",
                response.status_code == 200,
                "Can reach external services",
                critical=False
            )
        except:
            self.add_check(
                "External Internet",
                False,
                "Cannot reach external services",
                critical=False
            )
    
    def check_configuration(self):
        """Check configuration settings."""
        print("\n" + "="*60)
        print("⚙️  CONFIGURATION CHECK")
        print("="*60)
        
        # Check MT5 configuration
        self.add_check(
            "MT5 Account Configured",
            bool(config.mt5.account),
            f"Account: {config.mt5.account or 'Not set'}",
            critical=True
        )
        
        self.add_check(
            "MT5 Password Configured",
            bool(config.mt5.password),
            "Password: [CONFIGURED]" if config.mt5.password else "Password: Not set",
            critical=True
        )
        
        self.add_check(
            "MT5 Server Configured",
            bool(config.mt5.server),
            f"Server: {config.mt5.server or 'Not set'}",
            critical=True
        )
        
        # Check webhook configuration
        secure_key = config.webhook.secret_key not in ["", "your_secret_webhook_key_here"]
        self.add_check(
            "Webhook Secret Key",
            secure_key,
            "Secure key configured" if secure_key else "Default/empty key",
            critical=True
        )
        
        # Check trading symbols
        self.add_check(
            "Trading Symbols",
            len(config.trading.symbols) > 0,
            f"Symbols: {len(config.trading.symbols)} configured",
            critical=True
        )
        
        # Check log configuration
        log_dir = Path(config.logging.file_path).parent
        self.add_check(
            "Log Directory Writable",
            log_dir.exists() and os.access(log_dir, os.W_OK),
            f"Log path: {config.logging.file_path}",
            critical=True
        )
    
    def check_security(self):
        """Check security settings."""
        print("\n" + "="*60)
        print("🔒 SECURITY CHECK")
        print("="*60)
        
        # Check if using default secret
        default_secret = config.webhook.secret_key == "your_secret_webhook_key_here"
        self.add_check(
            "Non-default Secret Key",
            not default_secret,
            "Using custom secret key" if not default_secret else "Using default secret key",
            critical=True
        )
        
        # Check secret key strength
        secret_strong = len(config.webhook.secret_key) >= 16
        self.add_check(
            "Secret Key Strength",
            secret_strong,
            f"Length: {len(config.webhook.secret_key)} chars (should be >= 16)",
            critical=False
        )
        
        # Check if running on default port
        default_port = config.webhook.port in [8000, 80, 443]
        self.add_check(
            "Non-standard Port",
            config.webhook.port not in [8000],
            f"Port: {config.webhook.port}",
            critical=False
        )
    
    def check_performance_baseline(self):
        """Check performance baseline."""
        print("\n" + "="*60)
        print("⚡ PERFORMANCE BASELINE CHECK")
        print("="*60)
        
        # Test response time
        try:
            start_time = time.time()
            response = requests.get(f"{self.base_url}/health", timeout=10)
            response_time = time.time() - start_time
            
            self.add_check(
                "Response Time",
                response_time < 1.0,
                f"Health endpoint: {response_time:.3f}s (should be < 1.0s)",
                critical=False
            )
        except Exception as e:
            self.add_check(
                "Response Time",
                False,
                f"Error: {str(e)}",
                critical=True
            )
        
        # Test webhook processing time
        try:
            webhook_payload = {
                "secret": config.webhook.secret_key,
                "action": "close_all",
                "symbol": "EURUSD",
                "comment": "Performance test"
            }
            
            start_time = time.time()
            response = requests.post(
                f"{self.base_url}/webhook",
                json=webhook_payload,
                timeout=10
            )
            webhook_time = time.time() - start_time
            
            self.add_check(
                "Webhook Processing Time",
                webhook_time < 2.0,
                f"Webhook: {webhook_time:.3f}s (should be < 2.0s)",
                critical=False
            )
        except Exception as e:
            self.add_check(
                "Webhook Processing Time",
                False,
                f"Error: {str(e)}",
                critical=False
            )
    
    def check_monitoring_capabilities(self):
        """Check monitoring and logging capabilities."""
        print("\n" + "="*60)
        print("📊 MONITORING CHECK")
        print("="*60)
        
        # Check log file exists and is recent
        log_file = Path(config.logging.file_path)
        if log_file.exists():
            log_age = time.time() - log_file.stat().st_mtime
            recent_logs = log_age < 300  # 5 minutes
            self.add_check(
                "Recent Log Activity",
                recent_logs,
                f"Last modified: {log_age:.0f}s ago",
                critical=False
            )
        else:
            self.add_check(
                "Log File Exists",
                False,
                f"Log file not found: {log_file}",
                critical=True
            )
        
        # Check status endpoint
        try:
            response = requests.get(f"{self.base_url}/status", timeout=5)
            status_available = response.status_code == 200
            self.add_check(
                "Status Endpoint",
                status_available,
                "Status endpoint accessible",
                critical=False
            )
            
            if status_available:
                status_data = response.json()
                has_positions = "positions" in status_data
                has_account = "account_info" in status_data
                self.add_check(
                    "Status Data Complete",
                    has_positions and has_account,
                    f"Positions: {has_positions}, Account: {has_account}",
                    critical=False
                )
        except Exception as e:
            self.add_check(
                "Status Endpoint",
                False,
                f"Error: {str(e)}",
                critical=False
            )
    
    def run_production_check(self):
        """Run all production readiness checks."""
        print("🏭 PRODUCTION READINESS CHECK")
        print("="*60)
        print(f"Target URL: {self.base_url}")
        print(f"Timestamp: {datetime.now().isoformat()}")
        
        # Run all checks
        self.check_system_resources()
        self.check_network_connectivity()
        self.check_configuration()
        self.check_security()
        self.check_performance_baseline()
        self.check_monitoring_capabilities()
        
        # Generate summary
        self.generate_production_report()
    
    def generate_production_report(self):
        """Generate production readiness report."""
        print("\n" + "="*60)
        print("📋 PRODUCTION READINESS REPORT")
        print("="*60)
        
        total_checks = len(self.checks)
        passed_checks = sum(1 for c in self.checks if c["passed"])
        critical_checks = [c for c in self.checks if c["critical"]]
        critical_passed = sum(1 for c in critical_checks if c["passed"])
        critical_failed = len(critical_checks) - critical_passed
        
        print(f"Total Checks: {total_checks}")
        print(f"Passed: {passed_checks} ✅")
        print(f"Failed: {total_checks - passed_checks} ❌")
        print(f"Critical Failed: {critical_failed} 🔴")
        
        # Production readiness assessment
        production_ready = critical_failed == 0
        
        if production_ready:
            print(f"\n🎉 PRODUCTION READY!")
            print("All critical checks passed. System is ready for production deployment.")
        else:
            print(f"\n⚠️  NOT PRODUCTION READY")
            print(f"❌ {critical_failed} critical issues must be resolved:")
            
            for check in self.checks:
                if check["critical"] and not check["passed"]:
                    print(f"   🔴 {check['name']}: {check['details']}")
        
        # Recommendations
        print(f"\n💡 RECOMMENDATIONS:")
        
        warnings = [c for c in self.checks if not c["critical"] and not c["passed"]]
        if warnings:
            print("Non-critical improvements:")
            for warning in warnings:
                print(f"   🟡 {warning['name']}: {warning['details']}")
        
        if production_ready:
            print("✅ Monitor system performance regularly")
            print("✅ Set up automated health checks")
            print("✅ Configure log rotation and monitoring")
            print("✅ Test disaster recovery procedures")
        
        # Save report
        report_file = f"production_check_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump({
                "timestamp": datetime.now().isoformat(),
                "production_ready": production_ready,
                "summary": {
                    "total_checks": total_checks,
                    "passed_checks": passed_checks,
                    "critical_failed": critical_failed
                },
                "checks": self.checks
            }, f, indent=2)
        
        print(f"\n📄 Detailed report saved to: {report_file}")
        
        return production_ready


def main():
    """Main production check function."""
    checker = ProductionChecker()
    production_ready = checker.run_production_check()
    
    # Exit with appropriate code
    sys.exit(0 if production_ready else 1)


if __name__ == "__main__":
    main()
