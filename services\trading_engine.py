"""Core trading engine that orchestrates webhook processing and trade execution."""

import asyncio
from datetime import datetime
from typing import Optional, List, Dict, Any

from models.trading_models import (
    WebhookAlert, MT5TradeRequest, MT5TradeResult, TradeAction, TradeRecord, TradeStatus
)
from services.mt5_service import MT5Service
from utils.schedule_manager import ScheduleManager
from config import config
from utils.logger import get_logger

logger = get_logger(__name__)


class TradingEngine:
    """Main trading engine that processes webhook alerts and executes trades."""
    
    def __init__(self):
        self.mt5_service = MT5Service()
        self.schedule_manager = ScheduleManager()
        self.trade_records: List[TradeRecord] = []
        self.initialized = False
    
    async def initialize(self) -> bool:
        """Initialize the trading engine."""
        try:
            logger.info("Initializing trading engine")
            
            # Connect to MT5
            if not self.mt5_service.connect():
                logger.error("Failed to connect to MT5")
                return False
            
            # Validate symbols
            invalid_symbols = []
            for symbol in config.trading.symbols:
                if not self.mt5_service.validate_symbol(symbol):
                    invalid_symbols.append(symbol)
            
            if invalid_symbols:
                logger.warning("Invalid symbols found", symbols=invalid_symbols)
            
            self.initialized = True
            logger.info("Trading engine initialized successfully")
            return True
            
        except Exception as e:
            logger.error("Failed to initialize trading engine", error=str(e))
            return False
    
    async def shutdown(self):
        """Shutdown the trading engine."""
        logger.info("Shutting down trading engine")
        self.mt5_service.disconnect()
        self.initialized = False
        logger.info("Trading engine shutdown complete")
    
    async def process_alert(self, alert: WebhookAlert) -> Optional[MT5TradeResult]:
        """Process a webhook alert and execute trades."""
        if not self.initialized:
            logger.error("Trading engine not initialized")
            return None
        
        # Create trade record
        trade_record = TradeRecord(
            webhook_data=alert.dict(),
            status=TradeStatus.PENDING
        )
        self.trade_records.append(trade_record)
        
        try:
            logger.info("Processing webhook alert", alert=alert.dict())
            
            # Check if trading is allowed
            if not self.schedule_manager.is_trading_allowed():
                error_msg = "Trading not allowed at this time"
                logger.warning(error_msg)
                trade_record.status = TradeStatus.FAILED
                trade_record.error_message = error_msg
                return None
            
            # Validate symbol
            if not self.mt5_service.validate_symbol(alert.symbol):
                error_msg = f"Invalid or not allowed symbol: {alert.symbol}"
                logger.error(error_msg)
                trade_record.status = TradeStatus.FAILED
                trade_record.error_message = error_msg
                return None
            
            # Handle different actions
            if alert.action == TradeAction.CLOSE_ALL:
                return await self._handle_close_all(alert, trade_record)
            elif alert.action == TradeAction.CLOSE:
                return await self._handle_close(alert, trade_record)
            elif alert.action in [TradeAction.BUY, TradeAction.SELL]:
                return await self._handle_trade(alert, trade_record)
            else:
                error_msg = f"Unknown action: {alert.action}"
                logger.error(error_msg)
                trade_record.status = TradeStatus.FAILED
                trade_record.error_message = error_msg
                return None
                
        except Exception as e:
            error_msg = f"Error processing alert: {str(e)}"
            logger.error(error_msg, alert=alert.dict())
            trade_record.status = TradeStatus.FAILED
            trade_record.error_message = error_msg
            return None
    
    async def _handle_close_all(self, alert: WebhookAlert, trade_record: TradeRecord) -> Optional[MT5TradeResult]:
        """Handle close all positions action."""
        logger.info("Closing all positions", symbol=alert.symbol)
        
        results = self.mt5_service.close_all_positions(alert.symbol)
        
        if results:
            successful_closes = sum(1 for r in results if r.success)
            logger.info(f"Closed {successful_closes}/{len(results)} positions")
            
            if successful_closes > 0:
                trade_record.status = TradeStatus.EXECUTED
                trade_record.executed_at = datetime.utcnow()
                # Return the first successful result
                return next((r for r in results if r.success), None)
        
        trade_record.status = TradeStatus.FAILED
        trade_record.error_message = "No positions to close or all closes failed"
        return None
    
    async def _handle_close(self, alert: WebhookAlert, trade_record: TradeRecord) -> Optional[MT5TradeResult]:
        """Handle close specific position action."""
        # For simplicity, close all positions for the symbol
        return await self._handle_close_all(alert, trade_record)
    
    async def _handle_trade(self, alert: WebhookAlert, trade_record: TradeRecord) -> Optional[MT5TradeResult]:
        """Handle buy/sell trade action."""
        logger.info("Processing trade signal", action=alert.action, symbol=alert.symbol)
        
        # Step 1: Close existing positions for this symbol
        existing_positions = self.mt5_service.get_positions(alert.symbol)
        if existing_positions:
            logger.info(f"Closing {len(existing_positions)} existing positions for {alert.symbol}")
            close_results = self.mt5_service.close_all_positions(alert.symbol)
            
            # Wait a moment for positions to close
            await asyncio.sleep(0.5)
        
        # Step 2: Calculate stop loss and take profit
        current_prices = self.mt5_service.get_current_price(alert.symbol)
        if not current_prices:
            error_msg = f"Failed to get current price for {alert.symbol}"
            logger.error(error_msg)
            trade_record.status = TradeStatus.FAILED
            trade_record.error_message = error_msg
            return None
        
        entry_price = current_prices['ask'] if alert.action == TradeAction.BUY else current_prices['bid']
        
        # Use provided SL/TP or calculate from pips
        stop_loss = alert.stop_loss
        take_profit = alert.take_profit
        
        if stop_loss is None and alert.stop_loss_pips:
            sl_tp_prices = self.mt5_service.calculate_sl_tp_prices(
                alert.symbol, alert.action, entry_price, alert.stop_loss_pips, None
            )
            stop_loss = sl_tp_prices['stop_loss']
        
        if take_profit is None and alert.take_profit_pips:
            sl_tp_prices = self.mt5_service.calculate_sl_tp_prices(
                alert.symbol, alert.action, entry_price, None, alert.take_profit_pips
            )
            take_profit = sl_tp_prices['take_profit']
        
        # Use defaults if still None
        if stop_loss is None or take_profit is None:
            sl_tp_prices = self.mt5_service.calculate_sl_tp_prices(
                alert.symbol, alert.action, entry_price
            )
            stop_loss = stop_loss or sl_tp_prices['stop_loss']
            take_profit = take_profit or sl_tp_prices['take_profit']
        
        # Step 3: Create and execute trade request
        trade_request = MT5TradeRequest(
            symbol=alert.symbol,
            action=alert.action,
            volume=alert.quantity,
            price=alert.price,
            stop_loss=stop_loss,
            take_profit=take_profit,
            comment=alert.comment or f"Webhook {alert.action} {alert.symbol}",
            magic=config.trading.magic_number
        )
        
        trade_record.mt5_request = trade_request.dict()
        
        # Execute the trade
        result = self.mt5_service.execute_trade(trade_request)
        trade_record.mt5_result = result.dict()
        
        if result.success:
            trade_record.status = TradeStatus.EXECUTED
            trade_record.executed_at = datetime.utcnow()
            logger.info("Trade executed successfully", result=result.dict())
        else:
            trade_record.status = TradeStatus.FAILED
            trade_record.error_message = result.error_message
            logger.error("Trade execution failed", error=result.error_message)
        
        return result
    
    def get_trade_history(self, limit: int = 100) -> List[TradeRecord]:
        """Get recent trade history."""
        return self.trade_records[-limit:] if self.trade_records else []
