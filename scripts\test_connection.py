"""Test script to verify MT5 connection and basic functionality."""

import sys
import asyncio
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from services.mt5_service import MT5Service
from utils.schedule_manager import ScheduleManager
from utils.logger import setup_logging, get_logger
from utils.trading_utils import log_system_status
from config import config

# Setup logging
setup_logging()
logger = get_logger(__name__)


async def test_mt5_connection():
    """Test MetaTrader 5 connection."""
    logger.info("Testing MT5 connection")
    
    mt5_service = MT5Service()
    
    try:
        # Test connection
        if not mt5_service.connect():
            logger.error("Failed to connect to MT5")
            return False
        
        logger.info("✅ MT5 connection successful")
        
        # Test account info
        account_info = mt5_service.get_account_info()
        if account_info:
            logger.info("Account Info", **account_info)
        
        # Test symbol validation
        logger.info("Testing symbol validation")
        for symbol in config.trading.symbols:
            is_valid = mt5_service.validate_symbol(symbol)
            logger.info(f"Symbol {symbol}: {'✅ Valid' if is_valid else '❌ Invalid'}")
        
        # Test current positions
        positions = mt5_service.get_positions()
        logger.info(f"Current positions: {len(positions)}")
        
        for pos in positions:
            logger.info(
                "Position",
                ticket=pos.ticket,
                symbol=pos.symbol,
                type=pos.type,
                volume=pos.volume,
                profit=pos.profit
            )
        
        return True
        
    except Exception as e:
        logger.error("Error testing MT5 connection", error=str(e))
        return False
    finally:
        mt5_service.disconnect()


def test_schedule_manager():
    """Test schedule manager functionality."""
    logger.info("Testing schedule manager")
    
    schedule_manager = ScheduleManager()
    
    # Test current status
    is_allowed = schedule_manager.is_trading_allowed()
    status_message = schedule_manager.get_trading_status_message()
    
    logger.info("Schedule Status", allowed=is_allowed, message=status_message)
    
    if schedule_manager.enabled:
        next_session = schedule_manager.get_next_trading_session()
        logger.info("Next trading session", time=next_session.isoformat())


async def test_webhook_payload_processing():
    """Test webhook payload processing."""
    logger.info("Testing webhook payload processing")
    
    from utils.trading_utils import validate_webhook_payload, create_test_webhook_payload
    
    # Test valid payload
    valid_payload = create_test_webhook_payload()
    is_valid = validate_webhook_payload(valid_payload)
    logger.info(f"Valid payload test: {'✅ Passed' if is_valid else '❌ Failed'}")
    
    # Test invalid payload
    invalid_payload = {"secret": "test"}  # Missing required fields
    is_valid = validate_webhook_payload(invalid_payload)
    logger.info(f"Invalid payload test: {'✅ Passed' if not is_valid else '❌ Failed'}")


async def main():
    """Main test function."""
    logger.info("Starting system tests")
    
    # Test MT5 connection
    mt5_success = await test_mt5_connection()
    
    # Test schedule manager
    test_schedule_manager()
    
    # Test webhook processing
    await test_webhook_payload_processing()
    
    # Summary
    logger.info("Test Summary")
    logger.info(f"MT5 Connection: {'✅ Passed' if mt5_success else '❌ Failed'}")
    
    if mt5_success:
        logger.info("🎉 All tests passed! System is ready for trading.")
    else:
        logger.error("❌ Some tests failed. Please check configuration.")


if __name__ == "__main__":
    asyncio.run(main())
