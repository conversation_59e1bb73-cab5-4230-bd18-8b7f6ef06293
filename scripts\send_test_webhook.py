"""<PERSON><PERSON><PERSON> to send test webhook alerts to the trading system."""

import requests
import json
import time
from datetime import datetime

from utils.trading_utils import create_test_webhook_payload


def send_webhook(url: str, payload: dict, timeout: int = 10):
    """Send webhook alert to the server."""
    try:
        print(f"Sending webhook to {url}")
        print(f"Payload: {json.dumps(payload, indent=2)}")
        
        response = requests.post(
            url,
            json=payload,
            timeout=timeout,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"Response Status: {response.status_code}")
        print(f"Response Body: {json.dumps(response.json(), indent=2)}")
        
        return response.status_code == 200
        
    except requests.exceptions.RequestException as e:
        print(f"Error sending webhook: {e}")
        return False


def test_buy_signal(base_url: str, secret: str):
    """Test BUY signal."""
    print("\n" + "="*50)
    print("Testing BUY Signal")
    print("="*50)
    
    payload = create_test_webhook_payload(
        action="buy",
        symbol="EURUSD",
        quantity=0.1,
        secret=secret
    )
    payload.update({
        "stop_loss_pips": 50,
        "take_profit_pips": 100,
        "comment": "Test BUY signal"
    })
    
    return send_webhook(f"{base_url}/webhook", payload)


def test_sell_signal(base_url: str, secret: str):
    """Test SELL signal."""
    print("\n" + "="*50)
    print("Testing SELL Signal")
    print("="*50)
    
    payload = create_test_webhook_payload(
        action="sell",
        symbol="EURUSD",
        quantity=0.1,
        secret=secret
    )
    payload.update({
        "stop_loss_pips": 50,
        "take_profit_pips": 100,
        "comment": "Test SELL signal"
    })
    
    return send_webhook(f"{base_url}/webhook", payload)


def test_close_all_signal(base_url: str, secret: str):
    """Test CLOSE_ALL signal."""
    print("\n" + "="*50)
    print("Testing CLOSE_ALL Signal")
    print("="*50)
    
    payload = create_test_webhook_payload(
        action="close_all",
        symbol="EURUSD",
        secret=secret
    )
    payload.update({
        "comment": "Test CLOSE_ALL signal"
    })
    
    return send_webhook(f"{base_url}/webhook", payload)


def check_server_status(base_url: str):
    """Check server status."""
    print("\n" + "="*50)
    print("Checking Server Status")
    print("="*50)
    
    try:
        # Health check
        response = requests.get(f"{base_url}/health", timeout=5)
        print(f"Health Status: {response.status_code}")
        print(f"Health Response: {json.dumps(response.json(), indent=2)}")
        
        # Detailed status
        response = requests.get(f"{base_url}/status", timeout=5)
        print(f"Status Response: {json.dumps(response.json(), indent=2)}")
        
        return response.status_code == 200
        
    except requests.exceptions.RequestException as e:
        print(f"Error checking server status: {e}")
        return False


def main():
    """Main test function."""
    # Configuration
    BASE_URL = "http://localhost:7000"
    SECRET_KEY = "your_secret_webhook_key_here"  # Change this to match your .env
    
    print("TradingView to MT5 Webhook Test Script")
    print("="*50)
    print(f"Server URL: {BASE_URL}")
    print(f"Timestamp: {datetime.now().isoformat()}")
    
    # Check if server is running
    if not check_server_status(BASE_URL):
        print("\n❌ Server is not running or not responding")
        print("Please start the server with: python main.py")
        return
    
    print("\n✅ Server is running")
    
    # Interactive menu
    while True:
        print("\n" + "="*50)
        print("Test Options:")
        print("1. Send BUY signal")
        print("2. Send SELL signal")
        print("3. Send CLOSE_ALL signal")
        print("4. Check server status")
        print("5. Exit")
        print("="*50)
        
        choice = input("Enter your choice (1-5): ").strip()
        
        if choice == "1":
            success = test_buy_signal(BASE_URL, SECRET_KEY)
            print(f"BUY signal test: {'✅ Success' if success else '❌ Failed'}")
            
        elif choice == "2":
            success = test_sell_signal(BASE_URL, SECRET_KEY)
            print(f"SELL signal test: {'✅ Success' if success else '❌ Failed'}")
            
        elif choice == "3":
            success = test_close_all_signal(BASE_URL, SECRET_KEY)
            print(f"CLOSE_ALL signal test: {'✅ Success' if success else '❌ Failed'}")
            
        elif choice == "4":
            success = check_server_status(BASE_URL)
            print(f"Status check: {'✅ Success' if success else '❌ Failed'}")
            
        elif choice == "5":
            print("Exiting...")
            break
            
        else:
            print("Invalid choice. Please enter 1-5.")
        
        # Wait before next action
        time.sleep(1)


if __name__ == "__main__":
    main()
