"""Tests for webhook functionality."""

import pytest
import asyncio
from unittest.mock import Mock, patch
from fastapi.testclient import Test<PERSON>lient

from services.webhook_server import app
from models.trading_models import WebhookAlert, TradeAction
from utils.trading_utils import create_test_webhook_payload


@pytest.fixture
def client():
    """Test client fixture."""
    return TestClient(app)


@pytest.fixture
def mock_trading_engine():
    """Mock trading engine fixture."""
    with patch('services.webhook_server.get_trading_engine') as mock:
        engine = Mock()
        engine.process_alert = Mock()
        mock.return_value = engine
        yield engine


def test_root_endpoint(client):
    """Test root endpoint."""
    response = client.get("/")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "running"
    assert "timestamp" in data


def test_health_endpoint(client, mock_trading_engine):
    """Test health check endpoint."""
    mock_trading_engine.mt5_service.is_connected.return_value = True
    mock_trading_engine.schedule_manager.is_trading_allowed.return_value = True
    mock_trading_engine.schedule_manager.get_trading_status_message.return_value = "Trading active"
    
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"
    assert data["mt5_connected"] is True


def test_webhook_valid_payload(client, mock_trading_engine):
    """Test webhook with valid payload."""
    payload = create_test_webhook_payload()
    
    # Mock successful trade result
    from models.trading_models import MT5TradeResult
    mock_result = MT5TradeResult(
        success=True,
        ticket=123456,
        volume=0.1,
        price=1.1000
    )
    mock_trading_engine.process_alert.return_value = mock_result
    
    response = client.post("/webhook", json=payload)
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"


def test_webhook_invalid_secret(client):
    """Test webhook with invalid secret."""
    payload = create_test_webhook_payload(secret="wrong_secret")
    
    response = client.post("/webhook", json=payload)
    assert response.status_code == 401


def test_webhook_invalid_json(client):
    """Test webhook with invalid JSON."""
    response = client.post("/webhook", data="invalid json")
    assert response.status_code == 400


def test_webhook_missing_fields(client):
    """Test webhook with missing required fields."""
    payload = {"secret": "test_secret"}  # Missing action and symbol
    
    response = client.post("/webhook", json=payload)
    assert response.status_code == 400


def test_webhook_alert_validation():
    """Test WebhookAlert model validation."""
    # Valid alert
    alert = WebhookAlert(
        secret="test",
        action=TradeAction.BUY,
        symbol="EURUSD",
        quantity=0.1
    )
    assert alert.symbol == "EURUSD"
    assert alert.action == TradeAction.BUY
    
    # Invalid quantity
    with pytest.raises(ValueError):
        WebhookAlert(
            secret="test",
            action=TradeAction.BUY,
            symbol="EURUSD",
            quantity=-0.1
        )


if __name__ == "__main__":
    pytest.main([__file__])
