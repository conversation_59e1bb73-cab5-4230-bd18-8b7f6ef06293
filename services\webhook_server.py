"""FastAPI webhook server for receiving TradingView alerts."""

from datetime import datetime
from typing import Dict, Any
import asyncio
import hashlib
import hmac

from fastapi import FastAPI, HTTPException, Request, Depends
from fastapi.security import HTT<PERSON><PERSON>earer
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

from models.trading_models import WebhookAlert, TradeRecord, TradeStatus
from config import config
from utils.logger import get_logger

logger = get_logger(__name__)

# Security
security = HTTPBearer()

# FastAPI app
app = FastAPI(
    title="TradingView to MT5 Webhook Server",
    description="Webhook server for receiving TradingView alerts and executing trades on MetaTrader 5",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global trading engine instance
trading_engine = None


def verify_webhook_signature(payload: bytes, signature: str, secret: str) -> bool:
    """Verify webhook signature for security."""
    if not signature:
        return False
    
    expected_signature = hmac.new(
        secret.encode('utf-8'),
        payload,
        hashlib.sha256
    ).hexdigest()
    
    return hmac.compare_digest(signature, expected_signature)


async def get_trading_engine():
    """Get trading engine instance."""
    global trading_engine
    if trading_engine is None:
        from services.trading_engine import TradingEngine
        trading_engine = TradingEngine()
        await trading_engine.initialize()
    return trading_engine


@app.on_event("startup")
async def startup_event():
    """Initialize services on startup."""
    logger.info("Starting webhook server")
    global trading_engine
    from services.trading_engine import TradingEngine
    trading_engine = TradingEngine()
    await trading_engine.initialize()
    logger.info("Webhook server started successfully")


@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown."""
    logger.info("Shutting down webhook server")
    global trading_engine
    if trading_engine:
        await trading_engine.shutdown()
    logger.info("Webhook server shutdown complete")


@app.get("/")
async def root():
    """Root endpoint for health check."""
    return {
        "status": "running",
        "service": "TradingView to MT5 Webhook Server",
        "timestamp": datetime.utcnow().isoformat(),
        "version": "1.0.0"
    }


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    engine = await get_trading_engine()
    mt5_status = engine.mt5_service.is_connected()
    
    return {
        "status": "healthy" if mt5_status else "unhealthy",
        "mt5_connected": mt5_status,
        "trading_allowed": engine.schedule_manager.is_trading_allowed(),
        "schedule_status": engine.schedule_manager.get_trading_status_message(),
        "timestamp": datetime.utcnow().isoformat()
    }


@app.get("/status")
async def get_status():
    """Get detailed system status."""
    engine = await get_trading_engine()
    
    # Get account info
    account_info = engine.mt5_service.get_account_info()
    
    # Get current positions
    positions = engine.mt5_service.get_positions()
    
    return {
        "mt5_connected": engine.mt5_service.is_connected(),
        "account_info": account_info,
        "positions": [pos.dict() for pos in positions],
        "trading_allowed": engine.schedule_manager.is_trading_allowed(),
        "schedule_status": engine.schedule_manager.get_trading_status_message(),
        "allowed_symbols": config.trading.symbols,
        "timestamp": datetime.utcnow().isoformat()
    }


@app.post("/webhook")
async def receive_webhook(request: Request):
    """Receive and process TradingView webhook alerts."""
    try:
        # Get raw body for signature verification
        body = await request.body()
        
        # Verify signature if provided
        signature = request.headers.get('X-Signature')
        if signature and not verify_webhook_signature(body, signature, config.webhook.secret_key):
            logger.warning("Invalid webhook signature")
            raise HTTPException(status_code=401, detail="Invalid signature")
        
        # Parse JSON payload
        try:
            payload = await request.json()
        except Exception as e:
            logger.error("Failed to parse webhook JSON", error=str(e))
            raise HTTPException(status_code=400, detail="Invalid JSON payload")
        
        # Validate webhook alert
        try:
            alert = WebhookAlert(**payload)
        except Exception as e:
            logger.error("Invalid webhook alert format", error=str(e), payload=payload)
            raise HTTPException(status_code=400, detail=f"Invalid alert format: {str(e)}")
        
        # Verify secret key
        if alert.secret != config.webhook.secret_key:
            logger.warning("Invalid webhook secret key")
            raise HTTPException(status_code=401, detail="Invalid secret key")
        
        logger.info("Received webhook alert", alert=alert.dict())
        
        # Process the alert
        engine = await get_trading_engine()
        result = await engine.process_alert(alert)
        
        return {
            "status": "success",
            "message": "Alert processed successfully",
            "trade_result": result.dict() if result else None,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Unexpected error processing webhook", error=str(e))
        raise HTTPException(status_code=500, detail="Internal server error")


@app.post("/close-all")
async def close_all_positions(symbol: str = None):
    """Close all positions for a symbol or all symbols."""
    try:
        engine = await get_trading_engine()
        results = engine.mt5_service.close_all_positions(symbol)
        
        return {
            "status": "success",
            "message": f"Closed positions for {symbol or 'all symbols'}",
            "results": [result.dict() for result in results],
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error("Failed to close positions", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


def run_server():
    """Run the webhook server."""
    logger.info(
        "Starting webhook server",
        host=config.webhook.host,
        port=config.webhook.port
    )
    
    uvicorn.run(
        "services.webhook_server:app",
        host=config.webhook.host,
        port=config.webhook.port,
        reload=False,
        log_level=config.logging.level.lower()
    )
