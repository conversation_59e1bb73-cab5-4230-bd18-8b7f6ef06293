# Database settings
DB_HOST=localhost
DB_PORT=5432
DB_NAME=tradingview
DB_USER=tvuser
DB_PASSWORD=tvpassword

# Redis settings
REDIS_HOST=localhost
REDIS_PORT=6379

# TradingView settings (refer to ReadMe on how to get this value)
TV_BROKER_URL=dummy_broker_url                                            
TV_ACCOUNT_ID=dummy_account_id_of_the_tradingaccount_attached_to_tradingview

# MT5 settings
MT5_ACCOUNT=dummy_account_number
MT5_PASSWORD=dummy_mt5_password
MT5_SERVER=dummy_mt5_account_server
MT5_TERMINAL_PATH=C:\Users\<USER>\AppData\Roaming\Fusion Markets MT5 Terminal\terminal64.exe


# MT5 Symbol Settings
# When your broker uses a consistent suffix pattern for most symbols
MT5_DEFAULT_SUFFIX=.r 

# When certain symbols need completely different names or different suffixes
MT5_SYMBOL_MAP={"BTCUSD": "BTCUSD.r", "ETHUSD": "ETHUSD.r", "XAUUSD": "XAUUSD.r"}

