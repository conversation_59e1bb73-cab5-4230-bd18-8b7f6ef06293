"""Configuration management for TradingView to MT5 webhook system."""

import os
from typing import Dict, List, Optional
from pydantic import Field
from pydantic_settings import BaseSettings


class MT5Config(BaseSettings):
    """MetaTrader 5 configuration."""
    account: int = Field(..., description="MT5 account number")
    password: str = Field(..., description="MT5 account password")
    server: str = Field(..., description="MT5 server name")
    terminal_path: str = Field(
        default="C:\\Program Files\\MetaTrader 5\\terminal64.exe",
        description="Path to MT5 terminal executable"
    )
    
    class Config:
        env_prefix = "MT5_"


class WebhookConfig(BaseSettings):
    """Webhook server configuration."""
    host: str = Field(default="0.0.0.0", description="Webhook server host")
    port: int = Field(default=8000, description="Webhook server port")
    secret_key: str = Field(..., description="Secret key for webhook authentication")
    
    class Config:
        env_prefix = "WEBHOOK_"


class TradingConfig(BaseSettings):
    """Trading configuration."""
    symbols: List[str] = Field(
        default=["EURUSD", "GBPUSD", "USDJPY"],
        description="Allowed trading symbols"
    )
    default_lot_size: float = Field(default=0.1, description="Default lot size")
    max_lot_size: float = Field(default=1.0, description="Maximum lot size")
    default_stop_loss_pips: int = Field(default=50, description="Default stop loss in pips")
    default_take_profit_pips: int = Field(default=100, description="Default take profit in pips")
    magic_number: int = Field(default=123456, description="Magic number for trades")
    
    class Config:
        env_prefix = "TRADING_"


class ScheduleConfig(BaseSettings):
    """Trading schedule configuration."""
    enabled: bool = Field(default=True, description="Enable time-based trading")
    start_time: str = Field(default="09:00", description="Trading start time (HH:MM)")
    end_time: str = Field(default="17:00", description="Trading end time (HH:MM)")
    timezone: str = Field(default="UTC", description="Timezone for trading schedule")
    trading_days: List[int] = Field(
        default=[0, 1, 2, 3, 4],  # Monday to Friday
        description="Trading days (0=Monday, 6=Sunday)"
    )
    
    class Config:
        env_prefix = "SCHEDULE_"


class LoggingConfig(BaseSettings):
    """Logging configuration."""
    level: str = Field(default="INFO", description="Logging level")
    file_path: str = Field(default="trading_system.log", description="Log file path")
    max_file_size: int = Field(default=10485760, description="Max log file size in bytes (10MB)")
    backup_count: int = Field(default=5, description="Number of backup log files")
    
    class Config:
        env_prefix = "LOG_"


class AppConfig(BaseSettings):
    """Main application configuration."""
    mt5: MT5Config = Field(default_factory=MT5Config)
    webhook: WebhookConfig = Field(default_factory=WebhookConfig)
    trading: TradingConfig = Field(default_factory=TradingConfig)
    schedule: ScheduleConfig = Field(default_factory=ScheduleConfig)
    logging: LoggingConfig = Field(default_factory=LoggingConfig)
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


# Global configuration instance
config = AppConfig()
