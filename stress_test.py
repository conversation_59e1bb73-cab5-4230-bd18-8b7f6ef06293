"""Stress testing for the trading system."""

import asyncio
import aiohttp
import time
import statistics
from datetime import datetime
from typing import List, Dict
import sys
from pathlib import Path

# Add current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from utils.logger import setup_logging, get_logger
from utils.trading_utils import create_test_webhook_payload
from config import config

# Setup logging
setup_logging()
logger = get_logger(__name__)


class StressTester:
    """High-volume stress tester for the trading system."""
    
    def __init__(self, base_url: str = "http://localhost:7000"):
        self.base_url = base_url
        self.secret_key = config.webhook.secret_key
        self.results = []
    
    async def send_webhook_async(self, session: aiohttp.ClientSession, payload: Dict) -> Dict:
        """Send async webhook request."""
        start_time = time.time()
        try:
            async with session.post(
                f"{self.base_url}/webhook",
                json=payload,
                timeout=aiohttp.ClientTimeout(total=10)
            ) as response:
                response_time = time.time() - start_time
                response_data = await response.json() if response.content_length else {}
                
                return {
                    "success": response.status == 200,
                    "status_code": response.status,
                    "response_time": response_time,
                    "response_data": response_data
                }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "response_time": time.time() - start_time
            }
    
    async def burst_test(self, num_requests: int = 50, concurrent: int = 10):
        """Test burst of concurrent requests."""
        print(f"\n🚀 BURST TEST: {num_requests} requests, {concurrent} concurrent")
        print("="*60)
        
        payloads = []
        symbols = ["EURUSD", "GBPUSD", "USDJPY", "XAUUSD"]
        actions = ["buy", "sell", "close_all"]
        
        # Generate test payloads
        for i in range(num_requests):
            payload = create_test_webhook_payload(
                action=actions[i % len(actions)],
                symbol=symbols[i % len(symbols)],
                secret=self.secret_key
            )
            payload["comment"] = f"Stress test {i+1}"
            payloads.append(payload)
        
        # Send requests in batches
        results = []
        start_time = time.time()
        
        async with aiohttp.ClientSession() as session:
            for i in range(0, num_requests, concurrent):
                batch = payloads[i:i+concurrent]
                tasks = [self.send_webhook_async(session, payload) for payload in batch]
                batch_results = await asyncio.gather(*tasks)
                results.extend(batch_results)
                
                # Small delay between batches to avoid overwhelming
                await asyncio.sleep(0.1)
        
        total_time = time.time() - start_time
        
        # Analyze results
        successful = sum(1 for r in results if r["success"])
        failed = len(results) - successful
        response_times = [r["response_time"] for r in results if "response_time" in r]
        
        print(f"✅ Successful: {successful}/{num_requests} ({successful/num_requests*100:.1f}%)")
        print(f"❌ Failed: {failed}")
        print(f"⏱️  Total time: {total_time:.2f}s")
        print(f"📊 Requests/sec: {num_requests/total_time:.2f}")
        
        if response_times:
            print(f"📈 Response times:")
            print(f"   Average: {statistics.mean(response_times):.3f}s")
            print(f"   Median: {statistics.median(response_times):.3f}s")
            print(f"   Min: {min(response_times):.3f}s")
            print(f"   Max: {max(response_times):.3f}s")
        
        return {
            "total_requests": num_requests,
            "successful": successful,
            "failed": failed,
            "total_time": total_time,
            "requests_per_second": num_requests/total_time,
            "response_times": response_times
        }
    
    async def sustained_load_test(self, duration_minutes: int = 5, requests_per_minute: int = 60):
        """Test sustained load over time."""
        print(f"\n⏰ SUSTAINED LOAD TEST: {duration_minutes} minutes, {requests_per_minute} req/min")
        print("="*60)
        
        total_requests = duration_minutes * requests_per_minute
        interval = 60.0 / requests_per_minute  # Seconds between requests
        
        results = []
        start_time = time.time()
        
        async with aiohttp.ClientSession() as session:
            for i in range(total_requests):
                payload = create_test_webhook_payload(
                    action="buy" if i % 2 == 0 else "sell",
                    symbol="EURUSD",
                    secret=self.secret_key
                )
                payload["comment"] = f"Sustained test {i+1}"
                
                result = await self.send_webhook_async(session, payload)
                results.append(result)
                
                # Progress update
                if (i + 1) % requests_per_minute == 0:
                    elapsed = time.time() - start_time
                    successful = sum(1 for r in results if r["success"])
                    print(f"   {i+1}/{total_requests} requests sent, {successful} successful, {elapsed:.1f}s elapsed")
                
                # Wait for next request
                await asyncio.sleep(interval)
        
        total_time = time.time() - start_time
        successful = sum(1 for r in results if r["success"])
        
        print(f"✅ Completed: {successful}/{total_requests} successful")
        print(f"⏱️  Total time: {total_time/60:.1f} minutes")
        
        return {
            "duration_minutes": total_time/60,
            "total_requests": total_requests,
            "successful": successful,
            "actual_rate": total_requests/(total_time/60)
        }
    
    async def memory_leak_test(self, cycles: int = 100):
        """Test for memory leaks with repeated operations."""
        print(f"\n🧠 MEMORY LEAK TEST: {cycles} cycles")
        print("="*60)
        
        import psutil
        import os
        
        # Get initial memory usage
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        print(f"Initial memory usage: {initial_memory:.2f} MB")
        
        async with aiohttp.ClientSession() as session:
            for cycle in range(cycles):
                # Send multiple requests in this cycle
                tasks = []
                for i in range(10):
                    payload = create_test_webhook_payload(
                        action="buy" if i % 2 == 0 else "sell",
                        symbol="EURUSD",
                        secret=self.secret_key
                    )
                    tasks.append(self.send_webhook_async(session, payload))
                
                await asyncio.gather(*tasks)
                
                # Check memory every 10 cycles
                if (cycle + 1) % 10 == 0:
                    current_memory = process.memory_info().rss / 1024 / 1024
                    memory_increase = current_memory - initial_memory
                    print(f"   Cycle {cycle+1}: {current_memory:.2f} MB (+{memory_increase:.2f} MB)")
        
        final_memory = process.memory_info().rss / 1024 / 1024
        total_increase = final_memory - initial_memory
        
        print(f"Final memory usage: {final_memory:.2f} MB")
        print(f"Total increase: {total_increase:.2f} MB")
        
        # Memory leak detection (simple heuristic)
        leak_threshold = 50  # MB
        if total_increase > leak_threshold:
            print(f"⚠️  Potential memory leak detected (>{leak_threshold} MB increase)")
        else:
            print("✅ No significant memory leak detected")
        
        return {
            "initial_memory_mb": initial_memory,
            "final_memory_mb": final_memory,
            "memory_increase_mb": total_increase,
            "potential_leak": total_increase > leak_threshold
        }
    
    async def error_recovery_test(self):
        """Test system recovery from errors."""
        print(f"\n🔄 ERROR RECOVERY TEST")
        print("="*60)
        
        async with aiohttp.ClientSession() as session:
            # Send invalid requests to trigger errors
            invalid_payloads = [
                {"secret": "wrong_secret", "action": "buy", "symbol": "EURUSD"},
                {"secret": self.secret_key, "action": "invalid", "symbol": "EURUSD"},
                {"secret": self.secret_key, "action": "buy", "symbol": "INVALID"},
                {"secret": self.secret_key, "action": "buy", "quantity": -1},
            ]
            
            print("Sending invalid requests to trigger errors...")
            for i, payload in enumerate(invalid_payloads):
                result = await self.send_webhook_async(session, payload)
                print(f"   Invalid request {i+1}: {'Expected failure' if not result['success'] else 'Unexpected success'}")
            
            # Now send valid requests to test recovery
            print("Testing recovery with valid requests...")
            recovery_results = []
            for i in range(5):
                payload = create_test_webhook_payload(secret=self.secret_key)
                payload["comment"] = f"Recovery test {i+1}"
                result = await self.send_webhook_async(session, payload)
                recovery_results.append(result["success"])
                print(f"   Recovery request {i+1}: {'✅ Success' if result['success'] else '❌ Failed'}")
            
            recovery_rate = sum(recovery_results) / len(recovery_results)
            print(f"Recovery rate: {recovery_rate*100:.1f}%")
            
            return {"recovery_rate": recovery_rate}
    
    async def run_all_stress_tests(self):
        """Run all stress tests."""
        print("💪 TRADING SYSTEM STRESS TEST SUITE")
        print("="*60)
        print(f"Target URL: {self.base_url}")
        print(f"Timestamp: {datetime.now().isoformat()}")
        
        # Check server health
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.base_url}/health") as response:
                    if response.status != 200:
                        print("❌ Server health check failed!")
                        return
        except Exception as e:
            print(f"❌ Cannot connect to server: {e}")
            return
        
        print("✅ Server is healthy, starting stress tests...")
        
        # Run stress tests
        results = {}
        
        # Burst test
        results["burst"] = await self.burst_test(num_requests=100, concurrent=20)
        
        # Sustained load test (shorter for demo)
        results["sustained"] = await self.sustained_load_test(duration_minutes=2, requests_per_minute=30)
        
        # Memory leak test
        results["memory"] = await self.memory_leak_test(cycles=50)
        
        # Error recovery test
        results["recovery"] = await self.error_recovery_test()
        
        # Generate final report
        self.generate_stress_report(results)
    
    def generate_stress_report(self, results: Dict):
        """Generate stress test report."""
        print("\n" + "="*60)
        print("📊 STRESS TEST SUMMARY")
        print("="*60)
        
        # Burst test summary
        if "burst" in results:
            burst = results["burst"]
            print(f"🚀 Burst Test:")
            print(f"   Success Rate: {burst['successful']}/{burst['total_requests']} ({burst['successful']/burst['total_requests']*100:.1f}%)")
            print(f"   Throughput: {burst['requests_per_second']:.2f} req/sec")
        
        # Sustained test summary
        if "sustained" in results:
            sustained = results["sustained"]
            print(f"⏰ Sustained Test:")
            print(f"   Success Rate: {sustained['successful']}/{sustained['total_requests']} ({sustained['successful']/sustained['total_requests']*100:.1f}%)")
            print(f"   Duration: {sustained['duration_minutes']:.1f} minutes")
        
        # Memory test summary
        if "memory" in results:
            memory = results["memory"]
            print(f"🧠 Memory Test:")
            print(f"   Memory Increase: {memory['memory_increase_mb']:.2f} MB")
            print(f"   Leak Detected: {'Yes' if memory['potential_leak'] else 'No'}")
        
        # Recovery test summary
        if "recovery" in results:
            recovery = results["recovery"]
            print(f"🔄 Recovery Test:")
            print(f"   Recovery Rate: {recovery['recovery_rate']*100:.1f}%")
        
        # Overall assessment
        print(f"\n🎯 OVERALL ASSESSMENT:")
        
        issues = []
        if results.get("burst", {}).get("successful", 0) / results.get("burst", {}).get("total_requests", 1) < 0.95:
            issues.append("Low success rate in burst test")
        
        if results.get("memory", {}).get("potential_leak", False):
            issues.append("Potential memory leak detected")
        
        if results.get("recovery", {}).get("recovery_rate", 1) < 0.8:
            issues.append("Poor error recovery")
        
        if not issues:
            print("✅ System passed all stress tests!")
            print("   Ready for high-volume production use.")
        else:
            print("⚠️  Issues found:")
            for issue in issues:
                print(f"   - {issue}")


async def main():
    """Main stress test function."""
    tester = StressTester()
    await tester.run_all_stress_tests()


if __name__ == "__main__":
    asyncio.run(main())
