"""Tests for MT5 service functionality."""

import pytest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

from services.mt5_service import MT5Service
from models.trading_models import MT5TradeRequest, TradeAction


@pytest.fixture
def mt5_service():
    """MT5 service fixture."""
    return MT5Service()


@pytest.fixture
def mock_mt5():
    """Mock MetaTrader5 module."""
    with patch('services.mt5_service.mt5') as mock:
        # Setup default mock returns
        mock.initialize.return_value = True
        mock.login.return_value = True
        mock.terminal_info.return_value = Mock()
        mock.account_info.return_value = Mock(
            login=********,
            server="TestServer",
            balance=10000.0,
            equity=10000.0
        )
        mock.symbol_info.return_value = Mock(
            name="EURUSD",
            digits=5,
            point=0.00001,
            spread=2,
            volume_min=0.01,
            volume_max=100.0,
            volume_step=0.01,
            bid=1.1000,
            ask=1.1002
        )
        mock.TRADE_RETCODE_DONE = 10009
        mock.ORDER_TYPE_BUY = 0
        mock.ORDER_TYPE_SELL = 1
        mock.TRADE_ACTION_DEAL = 1
        mock.ORDER_TIME_GTC = 0
        mock.ORDER_FILLING_IOC = 1
        
        yield mock


def test_mt5_connection(mt5_service, mock_mt5):
    """Test MT5 connection."""
    result = mt5_service.connect()
    assert result is True
    assert mt5_service.connected is True
    
    mock_mt5.initialize.assert_called_once()
    mock_mt5.login.assert_called_once()


def test_mt5_connection_failure(mt5_service, mock_mt5):
    """Test MT5 connection failure."""
    mock_mt5.initialize.return_value = False
    mock_mt5.last_error.return_value = (1, "Connection failed")
    
    result = mt5_service.connect()
    assert result is False
    assert mt5_service.connected is False


def test_get_symbol_info(mt5_service, mock_mt5):
    """Test getting symbol information."""
    mt5_service.connected = True
    
    symbol_info = mt5_service.get_symbol_info("EURUSD")
    
    assert symbol_info is not None
    assert symbol_info['name'] == "EURUSD"
    assert symbol_info['digits'] == 5
    assert symbol_info['point'] == 0.00001


def test_get_current_price(mt5_service, mock_mt5):
    """Test getting current price."""
    mt5_service.connected = True
    
    prices = mt5_service.get_current_price("EURUSD")
    
    assert prices is not None
    assert 'bid' in prices
    assert 'ask' in prices
    assert prices['bid'] == 1.1000
    assert prices['ask'] == 1.1002


def test_calculate_sl_tp_prices(mt5_service, mock_mt5):
    """Test stop loss and take profit calculation."""
    mt5_service.connected = True
    
    # Test BUY order
    sl_tp = mt5_service.calculate_sl_tp_prices(
        "EURUSD", TradeAction.BUY, 1.1000, 50, 100
    )
    
    assert sl_tp['stop_loss'] == 1.0995  # 1.1000 - 50 * 0.00001
    assert sl_tp['take_profit'] == 1.1010  # 1.1000 + 100 * 0.00001
    
    # Test SELL order
    sl_tp = mt5_service.calculate_sl_tp_prices(
        "EURUSD", TradeAction.SELL, 1.1000, 50, 100
    )
    
    assert sl_tp['stop_loss'] == 1.1005  # 1.1000 + 50 * 0.00001
    assert sl_tp['take_profit'] == 1.0990  # 1.1000 - 100 * 0.00001


def test_validate_symbol(mt5_service, mock_mt5):
    """Test symbol validation."""
    mt5_service.connected = True
    
    # Mock config
    with patch('services.mt5_service.config') as mock_config:
        mock_config.trading.symbols = ["EURUSD", "GBPUSD"]
        
        # Valid symbol
        assert mt5_service.validate_symbol("EURUSD") is True
        
        # Invalid symbol (not in config)
        assert mt5_service.validate_symbol("INVALID") is False


def test_execute_trade(mt5_service, mock_mt5):
    """Test trade execution."""
    mt5_service.connected = True
    
    # Mock successful order
    mock_result = Mock()
    mock_result.retcode = 10009  # TRADE_RETCODE_DONE
    mock_result.order = 123456
    mock_result.price = 1.1000
    mock_mt5.order_send.return_value = mock_result
    
    # Mock config
    with patch('services.mt5_service.config') as mock_config:
        mock_config.trading.symbols = ["EURUSD"]
        mock_config.trading.max_lot_size = 1.0
        mock_config.trading.magic_number = 123456
        
        trade_request = MT5TradeRequest(
            symbol="EURUSD",
            action=TradeAction.BUY,
            volume=0.1,
            stop_loss=1.0950,
            take_profit=1.1050
        )
        
        result = mt5_service.execute_trade(trade_request)
        
        assert result.success is True
        assert result.ticket == 123456
        assert result.price == 1.1000


def test_context_manager(mt5_service, mock_mt5):
    """Test context manager functionality."""
    with mt5_service as service:
        assert service.connected is True
    
    mock_mt5.shutdown.assert_called_once()


if __name__ == "__main__":
    pytest.main([__file__])
