"""Advanced test scenarios for the trading system."""

import asyncio
import json
import time
import requests
from datetime import datetime, timedelta
from typing import List, Dict, Any
import sys
from pathlib import Path

# Add current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from utils.logger import setup_logging, get_logger
from utils.trading_utils import create_test_webhook_payload
from config import config

# Setup logging
setup_logging()
logger = get_logger(__name__)


class TradingSystemTester:
    """Comprehensive trading system tester."""
    
    def __init__(self, base_url: str = "http://localhost:7000"):
        self.base_url = base_url
        self.secret_key = config.webhook.secret_key
        self.test_results = []
    
    def log_test_result(self, test_name: str, success: bool, details: str = ""):
        """Log test result."""
        status = "✅ PASS" if success else "❌ FAIL"
        result = {
            "test": test_name,
            "success": success,
            "details": details,
            "timestamp": datetime.now().isoformat()
        }
        self.test_results.append(result)
        logger.info(f"{status} - {test_name}", details=details)
        print(f"{status} - {test_name}")
        if details:
            print(f"    Details: {details}")
    
    def send_webhook(self, payload: Dict[str, Any], timeout: int = 10) -> Dict[str, Any]:
        """Send webhook request."""
        try:
            response = requests.post(
                f"{self.base_url}/webhook",
                json=payload,
                timeout=timeout,
                headers={'Content-Type': 'application/json'}
            )
            return {
                "success": response.status_code == 200,
                "status_code": response.status_code,
                "response": response.json() if response.content else {}
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    def check_server_health(self) -> bool:
        """Check if server is running and healthy."""
        try:
            response = requests.get(f"{self.base_url}/health", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def test_server_connectivity(self):
        """Test 1: Server connectivity and health."""
        print("\n" + "="*60)
        print("TEST 1: SERVER CONNECTIVITY")
        print("="*60)
        
        # Test root endpoint
        try:
            response = requests.get(f"{self.base_url}/", timeout=5)
            self.log_test_result(
                "Root endpoint", 
                response.status_code == 200,
                f"Status: {response.status_code}"
            )
        except Exception as e:
            self.log_test_result("Root endpoint", False, str(e))
        
        # Test health endpoint
        health_ok = self.check_server_health()
        self.log_test_result("Health endpoint", health_ok)
        
        # Test status endpoint
        try:
            response = requests.get(f"{self.base_url}/status", timeout=5)
            self.log_test_result(
                "Status endpoint", 
                response.status_code == 200,
                f"MT5 Connected: {response.json().get('mt5_connected', 'Unknown')}"
            )
        except Exception as e:
            self.log_test_result("Status endpoint", False, str(e))
    
    def test_webhook_authentication(self):
        """Test 2: Webhook authentication scenarios."""
        print("\n" + "="*60)
        print("TEST 2: WEBHOOK AUTHENTICATION")
        print("="*60)
        
        # Test valid secret
        payload = create_test_webhook_payload(secret=self.secret_key)
        result = self.send_webhook(payload)
        self.log_test_result(
            "Valid secret key",
            result["success"],
            result.get("error", "")
        )
        
        # Test invalid secret
        payload = create_test_webhook_payload(secret="wrong_secret")
        result = self.send_webhook(payload)
        self.log_test_result(
            "Invalid secret key (should fail)",
            result["status_code"] == 401,
            f"Status: {result.get('status_code', 'Unknown')}"
        )
        
        # Test missing secret
        payload = create_test_webhook_payload()
        del payload["secret"]
        result = self.send_webhook(payload)
        self.log_test_result(
            "Missing secret key (should fail)",
            not result["success"],
            f"Status: {result.get('status_code', 'Unknown')}"
        )
    
    def test_webhook_payload_validation(self):
        """Test 3: Webhook payload validation."""
        print("\n" + "="*60)
        print("TEST 3: PAYLOAD VALIDATION")
        print("="*60)
        
        # Test valid payload
        payload = create_test_webhook_payload(secret=self.secret_key)
        result = self.send_webhook(payload)
        self.log_test_result("Valid payload", result["success"])
        
        # Test invalid action
        payload = create_test_webhook_payload(secret=self.secret_key)
        payload["action"] = "invalid_action"
        result = self.send_webhook(payload)
        self.log_test_result(
            "Invalid action (should fail)",
            not result["success"],
            f"Status: {result.get('status_code', 'Unknown')}"
        )
        
        # Test missing symbol
        payload = create_test_webhook_payload(secret=self.secret_key)
        del payload["symbol"]
        result = self.send_webhook(payload)
        self.log_test_result(
            "Missing symbol (should fail)",
            not result["success"]
        )
        
        # Test invalid quantity
        payload = create_test_webhook_payload(secret=self.secret_key)
        payload["quantity"] = -0.1
        result = self.send_webhook(payload)
        self.log_test_result(
            "Negative quantity (should fail)",
            not result["success"]
        )
    
    def test_trading_actions(self):
        """Test 4: Different trading actions."""
        print("\n" + "="*60)
        print("TEST 4: TRADING ACTIONS")
        print("="*60)
        
        # Test BUY action
        payload = create_test_webhook_payload(
            action="buy",
            symbol="EURUSD",
            secret=self.secret_key
        )
        result = self.send_webhook(payload)
        self.log_test_result("BUY action", result["success"])
        
        time.sleep(1)  # Small delay between trades
        
        # Test SELL action
        payload = create_test_webhook_payload(
            action="sell",
            symbol="EURUSD",
            secret=self.secret_key
        )
        result = self.send_webhook(payload)
        self.log_test_result("SELL action", result["success"])
        
        time.sleep(1)
        
        # Test CLOSE_ALL action
        payload = create_test_webhook_payload(
            action="close_all",
            symbol="EURUSD",
            secret=self.secret_key
        )
        result = self.send_webhook(payload)
        self.log_test_result("CLOSE_ALL action", result["success"])
    
    def test_symbol_validation(self):
        """Test 5: Symbol validation."""
        print("\n" + "="*60)
        print("TEST 5: SYMBOL VALIDATION")
        print("="*60)
        
        # Test valid symbols from config
        for symbol in config.trading.symbols[:2]:  # Test first 2 symbols
            payload = create_test_webhook_payload(
                symbol=symbol,
                secret=self.secret_key
            )
            result = self.send_webhook(payload)
            self.log_test_result(f"Valid symbol: {symbol}", result["success"])
            time.sleep(0.5)
        
        # Test invalid symbol
        payload = create_test_webhook_payload(
            symbol="INVALID",
            secret=self.secret_key
        )
        result = self.send_webhook(payload)
        self.log_test_result(
            "Invalid symbol (should fail)",
            not result["success"] or "Invalid" in str(result.get("response", {}))
        )
    
    def test_stop_loss_take_profit(self):
        """Test 6: Stop loss and take profit scenarios."""
        print("\n" + "="*60)
        print("TEST 6: STOP LOSS & TAKE PROFIT")
        print("="*60)
        
        # Test with pips
        payload = create_test_webhook_payload(secret=self.secret_key)
        payload.update({
            "stop_loss_pips": 50,
            "take_profit_pips": 100
        })
        result = self.send_webhook(payload)
        self.log_test_result("SL/TP with pips", result["success"])
        
        time.sleep(1)
        
        # Test with exact prices
        payload = create_test_webhook_payload(secret=self.secret_key)
        payload.update({
            "stop_loss": 1.0950,
            "take_profit": 1.1050
        })
        result = self.send_webhook(payload)
        self.log_test_result("SL/TP with exact prices", result["success"])
    
    def test_concurrent_requests(self):
        """Test 7: Concurrent webhook requests."""
        print("\n" + "="*60)
        print("TEST 7: CONCURRENT REQUESTS")
        print("="*60)
        
        import threading
        
        results = []
        
        def send_concurrent_webhook(symbol: str):
            payload = create_test_webhook_payload(
                symbol=symbol,
                secret=self.secret_key
            )
            result = self.send_webhook(payload)
            results.append(result["success"])
        
        # Send 3 concurrent requests
        threads = []
        symbols = ["EURUSD", "GBPUSD", "USDJPY"]
        
        for symbol in symbols:
            thread = threading.Thread(target=send_concurrent_webhook, args=(symbol,))
            threads.append(thread)
            thread.start()
        
        for thread in threads:
            thread.join()
        
        success_count = sum(results)
        self.log_test_result(
            "Concurrent requests",
            success_count >= 2,  # At least 2 should succeed
            f"{success_count}/{len(symbols)} succeeded"
        )
    
    def test_error_scenarios(self):
        """Test 8: Error handling scenarios."""
        print("\n" + "="*60)
        print("TEST 8: ERROR HANDLING")
        print("="*60)
        
        # Test malformed JSON
        try:
            response = requests.post(
                f"{self.base_url}/webhook",
                data="invalid json",
                headers={'Content-Type': 'application/json'},
                timeout=5
            )
            self.log_test_result(
                "Malformed JSON (should fail)",
                response.status_code == 400,
                f"Status: {response.status_code}"
            )
        except Exception as e:
            self.log_test_result("Malformed JSON test", False, str(e))
        
        # Test empty payload
        try:
            response = requests.post(
                f"{self.base_url}/webhook",
                json={},
                timeout=5
            )
            self.log_test_result(
                "Empty payload (should fail)",
                response.status_code in [400, 422],
                f"Status: {response.status_code}"
            )
        except Exception as e:
            self.log_test_result("Empty payload test", False, str(e))
    
    def run_all_tests(self):
        """Run all test scenarios."""
        print("🧪 COMPREHENSIVE TRADING SYSTEM TEST SUITE")
        print("="*60)
        print(f"Target URL: {self.base_url}")
        print(f"Timestamp: {datetime.now().isoformat()}")
        
        # Check if server is running
        if not self.check_server_health():
            print("\n❌ Server is not running or not responding!")
            print("Please start the server with: python main.py")
            return False
        
        print("\n✅ Server is running, starting tests...")
        
        # Run all test scenarios
        self.test_server_connectivity()
        self.test_webhook_authentication()
        self.test_webhook_payload_validation()
        self.test_trading_actions()
        self.test_symbol_validation()
        self.test_stop_loss_take_profit()
        self.test_concurrent_requests()
        self.test_error_scenarios()
        
        # Generate summary
        self.generate_test_summary()
        
        return True
    
    def generate_test_summary(self):
        """Generate test summary report."""
        print("\n" + "="*60)
        print("📊 TEST SUMMARY REPORT")
        print("="*60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for r in self.test_results if r["success"])
        failed_tests = total_tests - passed_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests} ✅")
        print(f"Failed: {failed_tests} ❌")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print(f"\n❌ FAILED TESTS:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"   - {result['test']}: {result.get('details', '')}")
        
        # Save detailed report
        report_file = f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(self.test_results, f, indent=2)
        
        print(f"\n📄 Detailed report saved to: {report_file}")
        
        if passed_tests == total_tests:
            print("\n🎉 ALL TESTS PASSED! System is ready for production.")
        else:
            print(f"\n⚠️  {failed_tests} tests failed. Please review and fix issues.")


def main():
    """Main test function."""
    tester = TradingSystemTester()
    tester.run_all_tests()


if __name__ == "__main__":
    main()
