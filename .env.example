# TradingView to MetaTrader 5 Webhook Trading System Configuration

# MetaTrader 5 Settings
MT5_ACCOUNT=********
MT5_PASSWORD=your_mt5_password
MT5_SERVER=YourBroker-Demo
MT5_TERMINAL_PATH=C:\Program Files\MetaTrader 5\terminal64.exe

# Webhook Server Settings
WEBHOOK_HOST=0.0.0.0
WEBHOOK_PORT=7000
WEBHOOK_SECRET_KEY=your_secret_webhook_key_here

# Trading Configuration
TRADING_SYMBOLS=["EURUSD", "GBPUSD", "USDJPY", "XAUUSD", "BTCUSD"]
TRADING_DEFAULT_LOT_SIZE=0.1
TRADING_MAX_LOT_SIZE=1.0
TRADING_DEFAULT_STOP_LOSS_PIPS=50
TRADING_DEFAULT_TAKE_PROFIT_PIPS=100
TRADING_MAGIC_NUMBER=123456

# Trading Schedule (24-hour format)
SCHEDULE_ENABLED=true
SCHEDULE_START_TIME=09:00
SCHEDULE_END_TIME=17:00
SCHEDULE_TIMEZONE=UTC
SCHEDULE_TRADING_DAYS=[0, 1, 2, 3, 4]

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE_PATH=trading_system.log
LOG_MAX_FILE_SIZE=********
LOG_BACKUP_COUNT=5
