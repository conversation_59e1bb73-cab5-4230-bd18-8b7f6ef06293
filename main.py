"""Main application entry point for TradingView to MT5 webhook system."""

import asyncio
import signal
import sys
from pathlib import Path

# Add current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from services.webhook_server import run_server
from services.trading_engine import TradingEngine
from config import config
from utils.logger import setup_logging, get_logger

# Setup logging
setup_logging()
logger = get_logger(__name__)


class TradingSystem:
    """Main trading system orchestrator."""
    
    def __init__(self):
        self.trading_engine = None
        self.running = False
    
    async def start(self):
        """Start the trading system."""
        try:
            logger.info("Starting TradingView to MT5 Trading System")
            
            # Initialize trading engine
            self.trading_engine = TradingEngine()
            if not await self.trading_engine.initialize():
                logger.error("Failed to initialize trading engine")
                return False
            
            # Log system status
            account_info = self.trading_engine.mt5_service.get_account_info()
            if account_info:
                logger.info(
                    "MT5 Account connected",
                    account=account_info['login'],
                    server=account_info['server'],
                    balance=account_info['balance'],
                    equity=account_info['equity']
                )
            
            # Log trading schedule
            schedule_status = self.trading_engine.schedule_manager.get_trading_status_message()
            logger.info("Trading schedule", status=schedule_status)
            
            # Log allowed symbols
            logger.info("Allowed trading symbols", symbols=config.trading.symbols)
            
            self.running = True
            logger.info("Trading system started successfully")
            return True
            
        except Exception as e:
            logger.error("Failed to start trading system", error=str(e))
            return False
    
    async def stop(self):
        """Stop the trading system."""
        logger.info("Stopping trading system")
        self.running = False
        
        if self.trading_engine:
            await self.trading_engine.shutdown()
        
        logger.info("Trading system stopped")
    
    def setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown."""
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, shutting down gracefully")
            asyncio.create_task(self.stop())
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)


async def main():
    """Main application function."""
    try:
        # Create and start trading system
        trading_system = TradingSystem()
        trading_system.setup_signal_handlers()
        
        if not await trading_system.start():
            logger.error("Failed to start trading system")
            sys.exit(1)
        
        # Start webhook server
        logger.info("Starting webhook server")
        run_server()
        
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    except Exception as e:
        logger.error("Unexpected error in main", error=str(e))
        sys.exit(1)
    finally:
        if 'trading_system' in locals():
            await trading_system.stop()


def run_standalone():
    """Run the system in standalone mode (without webhook server)."""
    async def standalone_main():
        trading_system = TradingSystem()
        trading_system.setup_signal_handlers()
        
        if not await trading_system.start():
            logger.error("Failed to start trading system")
            return
        
        logger.info("Trading system running in standalone mode")
        logger.info("Press Ctrl+C to stop")
        
        try:
            while trading_system.running:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            logger.info("Received keyboard interrupt")
        finally:
            await trading_system.stop()
    
    asyncio.run(standalone_main())


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="TradingView to MT5 Trading System")
    parser.add_argument(
        "--mode",
        choices=["server", "standalone"],
        default="server",
        help="Run mode: server (with webhook) or standalone (MT5 only)"
    )
    
    args = parser.parse_args()
    
    if args.mode == "server":
        asyncio.run(main())
    else:
        run_standalone()
