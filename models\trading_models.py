"""Trading data models for webhook and MT5 integration."""

from datetime import datetime
from enum import Enum
from typing import Optional, Dict, Any
from pydantic import BaseModel, Field, validator


class TradeAction(str, Enum):
    """Trading action types."""
    BUY = "buy"
    SELL = "sell"
    CLOSE = "close"
    CLOSE_ALL = "close_all"


class OrderType(str, Enum):
    """Order types."""
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"


class TradeStatus(str, Enum):
    """Trade status."""
    PENDING = "pending"
    EXECUTED = "executed"
    FAILED = "failed"
    CLOSED = "closed"


class WebhookAlert(BaseModel):
    """TradingView webhook alert model."""
    secret: str = Field(..., description="Webhook secret key for authentication")
    action: TradeAction = Field(..., description="Trading action to perform")
    symbol: str = Field(..., description="Trading symbol")
    quantity: float = Field(default=0.1, description="Trade quantity/lot size")
    order_type: OrderType = Field(default=OrderType.MARKET, description="Order type")
    price: Optional[float] = Field(None, description="Entry price for limit/stop orders")
    stop_loss: Optional[float] = Field(None, description="Stop loss price")
    take_profit: Optional[float] = Field(None, description="Take profit price")
    stop_loss_pips: Optional[int] = Field(None, description="Stop loss in pips")
    take_profit_pips: Optional[int] = Field(None, description="Take profit in pips")
    comment: Optional[str] = Field(None, description="Trade comment")
    timestamp: Optional[datetime] = Field(default_factory=datetime.utcnow)
    
    @validator('symbol')
    def validate_symbol(cls, v):
        """Validate and normalize symbol."""
        return v.upper().strip()
    
    @validator('quantity')
    def validate_quantity(cls, v):
        """Validate quantity is positive."""
        if v <= 0:
            raise ValueError("Quantity must be positive")
        return v


class MT5TradeRequest(BaseModel):
    """MetaTrader 5 trade request model."""
    symbol: str
    action: TradeAction
    volume: float
    price: Optional[float] = None
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    comment: Optional[str] = None
    magic: int = Field(default=123456)


class MT5TradeResult(BaseModel):
    """MetaTrader 5 trade result model."""
    success: bool
    ticket: Optional[int] = None
    order_id: Optional[int] = None
    volume: Optional[float] = None
    price: Optional[float] = None
    error_code: Optional[int] = None
    error_message: Optional[str] = None
    comment: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class Position(BaseModel):
    """Trading position model."""
    ticket: int
    symbol: str
    volume: float
    type: str  # "buy" or "sell"
    open_price: float
    current_price: Optional[float] = None
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    profit: Optional[float] = None
    comment: Optional[str] = None
    open_time: datetime
    magic: int


class TradeRecord(BaseModel):
    """Complete trade record for database storage."""
    id: Optional[int] = None
    webhook_data: Dict[str, Any]
    mt5_request: Optional[Dict[str, Any]] = None
    mt5_result: Optional[Dict[str, Any]] = None
    status: TradeStatus = TradeStatus.PENDING
    created_at: datetime = Field(default_factory=datetime.utcnow)
    executed_at: Optional[datetime] = None
    closed_at: Optional[datetime] = None
    error_message: Optional[str] = None
