# PowerShell installation script for TradingView to MT5 Trading System
# Run this script as Administrator

param(
    [switch]$SkipPython,
    [switch]$SkipDependencies,
    [string]$PythonVersion = "3.11"
)

Write-Host "TradingView to MetaTrader 5 Trading System - Installation Script" -ForegroundColor Green
Write-Host "=================================================================" -ForegroundColor Green

# Check if running as Administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "ERROR: This script must be run as Administrator!" -ForegroundColor Red
    Write-Host "Please right-click PowerShell and select 'Run as Administrator'" -ForegroundColor Yellow
    exit 1
}

# Function to check if command exists
function Test-Command($command) {
    try {
        Get-Command $command -ErrorAction Stop
        return $true
    } catch {
        return $false
    }
}

# Check Python installation
if (-not $SkipPython) {
    Write-Host "`nChecking Python installation..." -ForegroundColor Yellow
    
    if (Test-Command "python") {
        $pythonVersion = python --version
        Write-Host "Found: $pythonVersion" -ForegroundColor Green
    } else {
        Write-Host "Python not found. Please install Python 3.8+ from https://python.org" -ForegroundColor Red
        Write-Host "Make sure to check 'Add Python to PATH' during installation" -ForegroundColor Yellow
        exit 1
    }
}

# Check MetaTrader 5 installation
Write-Host "`nChecking MetaTrader 5 installation..." -ForegroundColor Yellow
$mt5Path = "C:\Program Files\MetaTrader 5\terminal64.exe"
if (Test-Path $mt5Path) {
    Write-Host "✅ MetaTrader 5 found at: $mt5Path" -ForegroundColor Green
} else {
    Write-Host "❌ MetaTrader 5 not found at expected location" -ForegroundColor Red
    Write-Host "Please install MetaTrader 5 or update MT5_TERMINAL_PATH in .env" -ForegroundColor Yellow
}

# Install Python dependencies
if (-not $SkipDependencies) {
    Write-Host "`nInstalling Python dependencies..." -ForegroundColor Yellow
    
    try {
        python -m pip install --upgrade pip
        python -m pip install -r requirements.txt
        Write-Host "✅ Dependencies installed successfully" -ForegroundColor Green
    } catch {
        Write-Host "❌ Failed to install dependencies" -ForegroundColor Red
        Write-Host "Error: $_" -ForegroundColor Red
        exit 1
    }
}

# Create .env file if it doesn't exist
Write-Host "`nSetting up configuration..." -ForegroundColor Yellow
if (-not (Test-Path ".env")) {
    Copy-Item ".env.example" ".env"
    Write-Host "✅ Created .env file from template" -ForegroundColor Green
    Write-Host "⚠️  Please edit .env with your MT5 credentials!" -ForegroundColor Yellow
} else {
    Write-Host "✅ .env file already exists" -ForegroundColor Green
}

# Create logs directory
if (-not (Test-Path "logs")) {
    New-Item -ItemType Directory -Path "logs"
    Write-Host "✅ Created logs directory" -ForegroundColor Green
}

# Configure Windows Firewall
Write-Host "`nConfiguring Windows Firewall..." -ForegroundColor Yellow
try {
    netsh advfirewall firewall add rule name="TradingView Webhook" dir=in action=allow protocol=TCP localport=8000 | Out-Null
    Write-Host "✅ Firewall rule added for port 8000" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Could not add firewall rule. You may need to do this manually." -ForegroundColor Yellow
}

# Test installation
Write-Host "`nTesting installation..." -ForegroundColor Yellow
try {
    python -c "import MetaTrader5; import fastapi; import uvicorn; print('✅ All imports successful')"
    Write-Host "✅ Installation test passed" -ForegroundColor Green
} catch {
    Write-Host "❌ Installation test failed" -ForegroundColor Red
    Write-Host "Some dependencies may not be installed correctly" -ForegroundColor Yellow
}

# Installation complete
Write-Host "`n=================================================================" -ForegroundColor Green
Write-Host "Installation Complete!" -ForegroundColor Green
Write-Host "=================================================================" -ForegroundColor Green

Write-Host "`nNext Steps:" -ForegroundColor Yellow
Write-Host "1. Edit .env file with your MT5 credentials:" -ForegroundColor White
Write-Host "   notepad .env" -ForegroundColor Cyan
Write-Host "`n2. Test the connection:" -ForegroundColor White
Write-Host "   python scripts/test_connection.py" -ForegroundColor Cyan
Write-Host "`n3. Start the trading system:" -ForegroundColor White
Write-Host "   python main.py" -ForegroundColor Cyan
Write-Host "`n4. Test webhooks:" -ForegroundColor White
Write-Host "   python scripts/send_test_webhook.py" -ForegroundColor Cyan

Write-Host "`nFor detailed setup instructions, see SETUP.md" -ForegroundColor Green
