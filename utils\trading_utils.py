"""Trading utility functions."""

from typing import Dict, Any, Optional
from datetime import datetime
import json

from models.trading_models import WebhookAlert, TradeAction
from utils.logger import get_logger

logger = get_logger(__name__)


def validate_webhook_payload(payload: Dict[str, Any]) -> bool:
    """Validate webhook payload structure."""
    required_fields = ['secret', 'action', 'symbol']
    
    for field in required_fields:
        if field not in payload:
            logger.error(f"Missing required field: {field}")
            return False
    
    # Validate action
    if payload['action'] not in [action.value for action in TradeAction]:
        logger.error(f"Invalid action: {payload['action']}")
        return False
    
    return True


def normalize_symbol(symbol: str) -> str:
    """Normalize trading symbol format."""
    # Remove any spaces and convert to uppercase
    normalized = symbol.strip().upper()
    
    # Common symbol mappings
    symbol_mappings = {
        'XAUUSD': 'GOLD',
        'XAGUSD': 'SILVER',
        'USOIL': 'CRUDE',
        'UKOIL': 'BRENT',
    }
    
    return symbol_mappings.get(normalized, normalized)


def calculate_position_size(
    account_balance: float,
    risk_percent: float = 2.0,
    stop_loss_pips: int = 50,
    pip_value: float = 1.0
) -> float:
    """Calculate position size based on risk management."""
    risk_amount = account_balance * (risk_percent / 100)
    position_size = risk_amount / (stop_loss_pips * pip_value)
    
    # Round to 2 decimal places
    return round(position_size, 2)


def format_trade_summary(alert: WebhookAlert, result: Optional[Any] = None) -> str:
    """Format trade summary for logging."""
    summary = f"{alert.action.upper()} {alert.symbol} {alert.quantity} lots"
    
    if alert.stop_loss_pips:
        summary += f" | SL: {alert.stop_loss_pips} pips"
    if alert.take_profit_pips:
        summary += f" | TP: {alert.take_profit_pips} pips"
    
    if result and hasattr(result, 'success'):
        status = "✅ SUCCESS" if result.success else "❌ FAILED"
        summary += f" | {status}"
        
        if result.success and hasattr(result, 'price'):
            summary += f" @ {result.price}"
    
    return summary


def create_test_webhook_payload(
    action: str = "buy",
    symbol: str = "EURUSD",
    quantity: float = 0.1,
    secret: str = "test_secret"
) -> Dict[str, Any]:
    """Create a test webhook payload for testing."""
    return {
        "secret": secret,
        "action": action,
        "symbol": symbol,
        "quantity": quantity,
        "stop_loss_pips": 50,
        "take_profit_pips": 100,
        "comment": "Test trade",
        "timestamp": datetime.utcnow().isoformat()
    }


def log_system_status(mt5_service, schedule_manager):
    """Log comprehensive system status."""
    # MT5 connection status
    mt5_connected = mt5_service.is_connected()
    logger.info("MT5 Connection", connected=mt5_connected)
    
    if mt5_connected:
        account_info = mt5_service.get_account_info()
        if account_info:
            logger.info(
                "Account Status",
                balance=account_info['balance'],
                equity=account_info['equity'],
                margin=account_info['margin'],
                free_margin=account_info['free_margin']
            )
        
        # Current positions
        positions = mt5_service.get_positions()
        logger.info("Open Positions", count=len(positions))
        
        for pos in positions:
            logger.info(
                "Position",
                ticket=pos.ticket,
                symbol=pos.symbol,
                type=pos.type,
                volume=pos.volume,
                profit=pos.profit
            )
    
    # Trading schedule status
    schedule_status = schedule_manager.get_trading_status_message()
    logger.info("Trading Schedule", status=schedule_status)
