# TradingView to MetaTrader 5 Webhook Trading System
# Core Dependencies

# Web Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0

# MetaTrader 5 Integration
MetaTrader5==5.0.4424

# HTTP Requests
requests==2.31.0
httpx==0.25.2

# Scheduling and Time Management
schedule==1.2.0
pytz==2023.3

# Configuration Management
python-dotenv==1.0.0
pydantic-settings==2.1.0

# Logging and Monitoring
structlog==23.2.0
colorama==0.4.6

# Data Processing
pandas==2.1.4
numpy==1.24.3

# Windows-specific
pywin32==306; sys_platform == 'win32'

# Development and Testing
pytest==7.4.3
pytest-asyncio==0.21.1
