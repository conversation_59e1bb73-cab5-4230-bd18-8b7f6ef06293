"""Master test runner for all testing scenarios."""

import asyncio
import sys
import subprocess
import time
from datetime import datetime
from pathlib import Path

# Add current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from utils.logger import setup_logging, get_logger

# Setup logging
setup_logging()
logger = get_logger(__name__)


class MasterTestRunner:
    """Orchestrates all testing scenarios."""
    
    def __init__(self):
        self.test_results = {}
        self.start_time = None
    
    def print_header(self, title: str):
        """Print formatted header."""
        print("\n" + "="*80)
        print(f"🧪 {title}")
        print("="*80)
    
    def print_section(self, title: str):
        """Print section header."""
        print(f"\n{'='*60}")
        print(f"📋 {title}")
        print(f"{'='*60}")
    
    def run_subprocess_test(self, script_name: str, description: str) -> bool:
        """Run a test script as subprocess."""
        print(f"\n🚀 Running {description}...")
        print(f"Script: {script_name}")
        
        try:
            result = subprocess.run([
                sys.executable, script_name
            ], capture_output=True, text=True, timeout=300)  # 5 minute timeout
            
            success = result.returncode == 0
            
            print(f"Exit Code: {result.returncode}")
            if result.stdout:
                print("STDOUT:")
                print(result.stdout)
            if result.stderr:
                print("STDERR:")
                print(result.stderr)
            
            return success
            
        except subprocess.TimeoutExpired:
            print("❌ Test timed out (5 minutes)")
            return False
        except Exception as e:
            print(f"❌ Error running test: {e}")
            return False
    
    async def run_async_test(self, module_name: str, description: str) -> bool:
        """Run an async test."""
        print(f"\n🚀 Running {description}...")
        print(f"Module: {module_name}")
        
        try:
            if module_name == "test_scenarios":
                from test_scenarios import TradingSystemTester
                tester = TradingSystemTester()
                return tester.run_all_tests()
            elif module_name == "stress_test":
                from stress_test import StressTester
                tester = StressTester()
                await tester.run_all_stress_tests()
                return True
            else:
                print(f"❌ Unknown async test module: {module_name}")
                return False
                
        except Exception as e:
            print(f"❌ Error running async test: {e}")
            return False
    
    def check_prerequisites(self) -> bool:
        """Check if all prerequisites are met."""
        self.print_section("PREREQUISITES CHECK")
        
        issues = []
        
        # Check if .env exists
        if not Path(".env").exists():
            issues.append("❌ .env file not found")
            print("❌ .env file not found - copy .env.example to .env")
        else:
            print("✅ .env file found")
        
        # Check if server might be running
        import requests
        try:
            response = requests.get("http://localhost:7000/health", timeout=2)
            if response.status_code == 200:
                print("✅ Server appears to be running")
            else:
                issues.append("⚠️  Server not responding properly")
                print("⚠️  Server not responding properly")
        except:
            issues.append("⚠️  Server not running - some tests will fail")
            print("⚠️  Server not running - start with 'python main.py'")
        
        # Check required files
        required_files = [
            "validate_system.py",
            "test_scenarios.py", 
            "stress_test.py",
            "production_check.py"
        ]
        
        for file in required_files:
            if Path(file).exists():
                print(f"✅ {file} found")
            else:
                issues.append(f"❌ {file} missing")
                print(f"❌ {file} missing")
        
        if issues:
            print(f"\n⚠️  {len(issues)} issues found:")
            for issue in issues:
                print(f"   {issue}")
            return False
        
        print("\n✅ All prerequisites met!")
        return True
    
    async def run_all_tests(self):
        """Run all test scenarios."""
        self.start_time = datetime.now()
        
        self.print_header("COMPREHENSIVE TRADING SYSTEM TEST SUITE")
        print(f"Started: {self.start_time.isoformat()}")
        print(f"Python: {sys.version}")
        print(f"Working Directory: {Path.cwd()}")
        
        # Check prerequisites
        if not self.check_prerequisites():
            print("\n❌ Prerequisites not met. Please fix issues and try again.")
            return False
        
        # Test 1: System Validation
        self.print_section("TEST 1: SYSTEM VALIDATION")
        self.test_results["system_validation"] = self.run_subprocess_test(
            "validate_system.py",
            "System Configuration and MT5 Connection Validation"
        )
        
        # Test 2: Unit Tests (if pytest available)
        self.print_section("TEST 2: UNIT TESTS")
        try:
            result = subprocess.run([
                sys.executable, "-m", "pytest", "tests/", "-v", "--tb=short"
            ], capture_output=True, text=True, timeout=120)
            
            self.test_results["unit_tests"] = result.returncode == 0
            print("PYTEST OUTPUT:")
            print(result.stdout)
            if result.stderr:
                print("PYTEST ERRORS:")
                print(result.stderr)
                
        except subprocess.TimeoutExpired:
            print("❌ Unit tests timed out")
            self.test_results["unit_tests"] = False
        except FileNotFoundError:
            print("⚠️  pytest not available, skipping unit tests")
            self.test_results["unit_tests"] = None
        except Exception as e:
            print(f"❌ Error running unit tests: {e}")
            self.test_results["unit_tests"] = False
        
        # Test 3: Functional Tests
        self.print_section("TEST 3: FUNCTIONAL TESTS")
        self.test_results["functional_tests"] = await self.run_async_test(
            "test_scenarios",
            "Comprehensive Functional Testing"
        )
        
        # Test 4: Stress Tests
        self.print_section("TEST 4: STRESS TESTS")
        print("⚠️  Stress tests may take several minutes...")
        self.test_results["stress_tests"] = await self.run_async_test(
            "stress_test",
            "High-Volume Stress Testing"
        )
        
        # Test 5: Production Readiness
        self.print_section("TEST 5: PRODUCTION READINESS")
        self.test_results["production_check"] = self.run_subprocess_test(
            "production_check.py",
            "Production Readiness Assessment"
        )
        
        # Generate final report
        self.generate_final_report()
        
        return self.overall_success()
    
    def overall_success(self) -> bool:
        """Determine overall test success."""
        critical_tests = ["system_validation", "functional_tests", "production_check"]
        
        for test in critical_tests:
            if not self.test_results.get(test, False):
                return False
        
        return True
    
    def generate_final_report(self):
        """Generate comprehensive final report."""
        end_time = datetime.now()
        duration = end_time - self.start_time
        
        self.print_header("FINAL TEST REPORT")
        print(f"Started: {self.start_time.isoformat()}")
        print(f"Completed: {end_time.isoformat()}")
        print(f"Duration: {duration}")
        
        print(f"\n📊 TEST RESULTS SUMMARY:")
        print(f"{'='*60}")
        
        test_descriptions = {
            "system_validation": "System Validation",
            "unit_tests": "Unit Tests",
            "functional_tests": "Functional Tests", 
            "stress_tests": "Stress Tests",
            "production_check": "Production Readiness"
        }
        
        passed = 0
        total = 0
        
        for test_key, description in test_descriptions.items():
            result = self.test_results.get(test_key)
            if result is None:
                status = "⚪ SKIPPED"
            elif result:
                status = "✅ PASSED"
                passed += 1
                total += 1
            else:
                status = "❌ FAILED"
                total += 1
            
            print(f"{status} - {description}")
        
        # Overall assessment
        print(f"\n🎯 OVERALL ASSESSMENT:")
        print(f"{'='*60}")
        
        if self.overall_success():
            print("🎉 ALL CRITICAL TESTS PASSED!")
            print("\n✅ Your trading system is ready for production!")
            print("\nNext steps:")
            print("1. Deploy to your production server")
            print("2. Configure TradingView alerts")
            print("3. Start with small position sizes")
            print("4. Monitor system performance")
        else:
            print("❌ CRITICAL TESTS FAILED")
            print("\n⚠️  System is NOT ready for production!")
            print("\nRequired actions:")
            
            failed_critical = []
            if not self.test_results.get("system_validation", False):
                failed_critical.append("Fix system configuration issues")
            if not self.test_results.get("functional_tests", False):
                failed_critical.append("Resolve functional test failures")
            if not self.test_results.get("production_check", False):
                failed_critical.append("Address production readiness issues")
            
            for action in failed_critical:
                print(f"   - {action}")
        
        print(f"\n📈 SUCCESS RATE: {passed}/{total} ({passed/total*100 if total > 0 else 0:.1f}%)")
        
        # Save detailed report
        import json
        report_file = f"master_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump({
                "timestamp": end_time.isoformat(),
                "duration_seconds": duration.total_seconds(),
                "overall_success": self.overall_success(),
                "test_results": self.test_results,
                "summary": {
                    "passed": passed,
                    "total": total,
                    "success_rate": passed/total if total > 0 else 0
                }
            }, f, indent=2)
        
        print(f"\n📄 Detailed report saved to: {report_file}")


async def main():
    """Main test runner function."""
    runner = MasterTestRunner()
    success = await runner.run_all_tests()
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    asyncio.run(main())
