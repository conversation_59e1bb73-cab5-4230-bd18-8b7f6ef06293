"""Comprehensive system validation script."""

import sys
import asyncio
import json
from pathlib import Path
from datetime import datetime

# Add current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from utils.logger import setup_logging, get_logger
from config import config

# Setup logging
setup_logging()
logger = get_logger(__name__)


def check_environment():
    """Check environment configuration."""
    print("\n" + "="*60)
    print("ENVIRONMENT VALIDATION")
    print("="*60)
    
    issues = []
    
    # Check .env file exists
    if not Path(".env").exists():
        issues.append("❌ .env file not found. Copy .env.example to .env")
    else:
        print("✅ .env file found")
    
    # Check MT5 configuration
    try:
        if not config.mt5.account:
            issues.append("❌ MT5_ACCOUNT not configured")
        else:
            print(f"✅ MT5 Account: {config.mt5.account}")
        
        if not config.mt5.password:
            issues.append("❌ MT5_PASSWORD not configured")
        else:
            print("✅ MT5 Password: [CONFIGURED]")
        
        if not config.mt5.server:
            issues.append("❌ MT5_SERVER not configured")
        else:
            print(f"✅ MT5 Server: {config.mt5.server}")
        
        # Check MT5 terminal path
        if not Path(config.mt5.terminal_path).exists():
            issues.append(f"❌ MT5 terminal not found at: {config.mt5.terminal_path}")
        else:
            print(f"✅ MT5 Terminal: {config.mt5.terminal_path}")
            
    except Exception as e:
        issues.append(f"❌ Configuration error: {e}")
    
    # Check webhook configuration
    if not config.webhook.secret_key or config.webhook.secret_key == "your_secret_webhook_key_here":
        issues.append("❌ WEBHOOK_SECRET_KEY not properly configured")
    else:
        print("✅ Webhook Secret: [CONFIGURED]")
    
    # Check trading symbols
    if not config.trading.symbols:
        issues.append("❌ No trading symbols configured")
    else:
        print(f"✅ Trading Symbols: {config.trading.symbols}")
    
    return issues


def check_dependencies():
    """Check Python dependencies."""
    print("\n" + "="*60)
    print("DEPENDENCY VALIDATION")
    print("="*60)
    
    required_modules = [
        'MetaTrader5',
        'fastapi',
        'uvicorn',
        'pydantic',
        'requests',
        'schedule',
        'pytz',
        'structlog',
        'colorama'
    ]
    
    issues = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError:
            issues.append(f"❌ {module} not installed")
            print(f"❌ {module}")
    
    return issues


async def check_mt5_connection():
    """Check MT5 connection."""
    print("\n" + "="*60)
    print("MT5 CONNECTION VALIDATION")
    print("="*60)
    
    issues = []
    
    try:
        from services.mt5_service import MT5Service
        
        mt5_service = MT5Service()
        
        if not mt5_service.connect():
            issues.append("❌ Failed to connect to MT5")
            print("❌ MT5 connection failed")
            return issues
        
        print("✅ MT5 connection successful")
        
        # Test account info
        account_info = mt5_service.get_account_info()
        if account_info:
            print(f"✅ Account: {account_info['login']}")
            print(f"✅ Server: {account_info['server']}")
            print(f"✅ Balance: {account_info['balance']} {account_info['currency']}")
            print(f"✅ Equity: {account_info['equity']} {account_info['currency']}")
        else:
            issues.append("❌ Failed to get account info")
        
        # Test symbols
        print("\nSymbol Validation:")
        for symbol in config.trading.symbols:
            if mt5_service.validate_symbol(symbol):
                symbol_info = mt5_service.get_symbol_info(symbol)
                print(f"✅ {symbol}: Spread={symbol_info['spread']}, Min Volume={symbol_info['volume_min']}")
            else:
                issues.append(f"❌ Invalid symbol: {symbol}")
                print(f"❌ {symbol}: Invalid or not available")
        
        mt5_service.disconnect()
        
    except Exception as e:
        issues.append(f"❌ MT5 connection error: {e}")
        print(f"❌ Error: {e}")
    
    return issues


def check_schedule_configuration():
    """Check trading schedule configuration."""
    print("\n" + "="*60)
    print("SCHEDULE VALIDATION")
    print("="*60)
    
    issues = []
    
    try:
        from utils.schedule_manager import ScheduleManager
        
        schedule_manager = ScheduleManager()
        
        print(f"✅ Schedule Enabled: {schedule_manager.enabled}")
        
        if schedule_manager.enabled:
            print(f"✅ Trading Hours: {schedule_manager.start_time} - {schedule_manager.end_time}")
            print(f"✅ Timezone: {config.schedule.timezone}")
            print(f"✅ Trading Days: {config.schedule.trading_days}")
            
            # Check current status
            is_allowed = schedule_manager.is_trading_allowed()
            status_msg = schedule_manager.get_trading_status_message()
            print(f"✅ Current Status: {status_msg}")
        else:
            print("✅ Schedule disabled - trading allowed 24/7")
            
    except Exception as e:
        issues.append(f"❌ Schedule configuration error: {e}")
        print(f"❌ Error: {e}")
    
    return issues


async def main():
    """Main validation function."""
    print("TradingView to MT5 Trading System - System Validation")
    print("="*60)
    print(f"Timestamp: {datetime.now().isoformat()}")
    
    all_issues = []
    
    # Run all checks
    all_issues.extend(check_environment())
    all_issues.extend(check_dependencies())
    all_issues.extend(await check_mt5_connection())
    all_issues.extend(check_schedule_configuration())
    
    # Summary
    print("\n" + "="*60)
    print("VALIDATION SUMMARY")
    print("="*60)
    
    if not all_issues:
        print("🎉 ALL VALIDATIONS PASSED!")
        print("\nYour system is ready for trading!")
        print("\nNext steps:")
        print("1. Start the system: python main.py")
        print("2. Test webhooks: python scripts/send_test_webhook.py")
        print("3. Configure TradingView alerts")
    else:
        print("❌ VALIDATION ISSUES FOUND:")
        for issue in all_issues:
            print(f"   {issue}")
        
        print("\nPlease fix these issues before running the system.")
        print("See SETUP.md for detailed instructions.")
    
    print(f"\nTotal Issues: {len(all_issues)}")
    return len(all_issues) == 0


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
