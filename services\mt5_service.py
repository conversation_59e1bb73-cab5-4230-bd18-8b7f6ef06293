"""MetaTrader 5 service for trading operations."""

import MetaTrader5 as mt5
from datetime import datetime
from typing import List, Optional, Dict, Any
import time

from models.trading_models import (
    MT5TradeRequest, MT5TradeResult, Position, TradeAction, OrderType
)
from config import config
from utils.logger import get_logger

logger = get_logger(__name__)


class MT5Service:
    """Service for MetaTrader 5 operations."""
    
    def __init__(self):
        self.connected = False
        self.account_info = None
        
    def connect(self) -> bool:
        """Connect to MetaTrader 5."""
        try:
            # Initialize MT5 connection
            if not mt5.initialize(path=config.mt5.terminal_path):
                error = mt5.last_error()
                logger.error("Failed to initialize MT5", error=error)
                return False
            
            # Login to account
            if not mt5.login(
                login=config.mt5.account,
                password=config.mt5.password,
                server=config.mt5.server
            ):
                error = mt5.last_error()
                logger.error("Failed to login to MT5", error=error)
                mt5.shutdown()
                return False
            
            # Get account info
            self.account_info = mt5.account_info()
            if self.account_info is None:
                logger.error("Failed to get account info")
                mt5.shutdown()
                return False
            
            self.connected = True
            logger.info(
                "Successfully connected to MT5",
                account=self.account_info.login,
                server=self.account_info.server,
                balance=self.account_info.balance,
                equity=self.account_info.equity
            )
            return True
            
        except Exception as e:
            logger.error("Exception during MT5 connection", error=str(e))
            return False
    
    def disconnect(self):
        """Disconnect from MetaTrader 5."""
        if self.connected:
            mt5.shutdown()
            self.connected = False
            logger.info("Disconnected from MT5")
    
    def is_connected(self) -> bool:
        """Check if connected to MT5."""
        return self.connected and mt5.terminal_info() is not None
    
    def get_symbol_info(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get symbol information."""
        if not self.is_connected():
            logger.error("Not connected to MT5")
            return None
        
        symbol_info = mt5.symbol_info(symbol)
        if symbol_info is None:
            logger.error(f"Symbol {symbol} not found")
            return None
        
        return {
            'name': symbol_info.name,
            'digits': symbol_info.digits,
            'point': symbol_info.point,
            'spread': symbol_info.spread,
            'volume_min': symbol_info.volume_min,
            'volume_max': symbol_info.volume_max,
            'volume_step': symbol_info.volume_step,
            'bid': symbol_info.bid,
            'ask': symbol_info.ask
        }
    
    def get_current_price(self, symbol: str) -> Optional[Dict[str, float]]:
        """Get current bid/ask prices for symbol."""
        symbol_info = self.get_symbol_info(symbol)
        if symbol_info:
            return {
                'bid': symbol_info['bid'],
                'ask': symbol_info['ask']
            }
        return None
    
    def calculate_sl_tp_prices(
        self, 
        symbol: str, 
        action: TradeAction, 
        entry_price: float,
        sl_pips: Optional[int] = None,
        tp_pips: Optional[int] = None
    ) -> Dict[str, Optional[float]]:
        """Calculate stop loss and take profit prices."""
        symbol_info = self.get_symbol_info(symbol)
        if not symbol_info:
            return {'stop_loss': None, 'take_profit': None}
        
        point = symbol_info['point']
        
        # Use default values if not provided
        sl_pips = sl_pips or config.trading.default_stop_loss_pips
        tp_pips = tp_pips or config.trading.default_take_profit_pips
        
        if action == TradeAction.BUY:
            stop_loss = entry_price - (sl_pips * point) if sl_pips else None
            take_profit = entry_price + (tp_pips * point) if tp_pips else None
        else:  # SELL
            stop_loss = entry_price + (sl_pips * point) if sl_pips else None
            take_profit = entry_price - (tp_pips * point) if tp_pips else None
        
        return {
            'stop_loss': stop_loss,
            'take_profit': take_profit
        }
    
    def get_positions(self, symbol: Optional[str] = None) -> List[Position]:
        """Get current positions."""
        if not self.is_connected():
            logger.error("Not connected to MT5")
            return []
        
        positions = mt5.positions_get(symbol=symbol) if symbol else mt5.positions_get()
        if positions is None:
            return []
        
        result = []
        for pos in positions:
            position = Position(
                ticket=pos.ticket,
                symbol=pos.symbol,
                volume=pos.volume,
                type="buy" if pos.type == mt5.POSITION_TYPE_BUY else "sell",
                open_price=pos.price_open,
                current_price=pos.price_current,
                stop_loss=pos.sl if pos.sl != 0 else None,
                take_profit=pos.tp if pos.tp != 0 else None,
                profit=pos.profit,
                comment=pos.comment,
                open_time=datetime.fromtimestamp(pos.time),
                magic=pos.magic
            )
            result.append(position)
        
        return result

    def close_position(self, ticket: int) -> MT5TradeResult:
        """Close a specific position."""
        if not self.is_connected():
            return MT5TradeResult(
                success=False,
                error_message="Not connected to MT5"
            )

        # Get position info
        position = mt5.positions_get(ticket=ticket)
        if not position:
            return MT5TradeResult(
                success=False,
                error_message=f"Position {ticket} not found"
            )

        position = position[0]

        # Prepare close request
        close_type = mt5.ORDER_TYPE_SELL if position.type == mt5.POSITION_TYPE_BUY else mt5.ORDER_TYPE_BUY

        request = {
            "action": mt5.TRADE_ACTION_DEAL,
            "symbol": position.symbol,
            "volume": position.volume,
            "type": close_type,
            "position": ticket,
            "magic": config.trading.magic_number,
            "comment": "Closed by webhook system",
            "type_time": mt5.ORDER_TIME_GTC,
            "type_filling": mt5.ORDER_FILLING_IOC,
        }

        # Send close order
        result = mt5.order_send(request)

        if result.retcode != mt5.TRADE_RETCODE_DONE:
            error_msg = f"Failed to close position {ticket}: {result.retcode} - {result.comment}"
            logger.error(error_msg)
            return MT5TradeResult(
                success=False,
                error_code=result.retcode,
                error_message=error_msg
            )

        logger.info(
            "Position closed successfully",
            ticket=ticket,
            symbol=position.symbol,
            volume=position.volume,
            order_id=result.order
        )

        return MT5TradeResult(
            success=True,
            ticket=ticket,
            order_id=result.order,
            volume=position.volume,
            price=result.price,
            comment="Position closed"
        )

    def close_all_positions(self, symbol: Optional[str] = None) -> List[MT5TradeResult]:
        """Close all positions for a symbol or all symbols."""
        positions = self.get_positions(symbol)
        results = []

        for position in positions:
            result = self.close_position(position.ticket)
            results.append(result)

            # Small delay between closes to avoid overwhelming the server
            time.sleep(0.1)

        logger.info(
            "Closed positions",
            symbol=symbol or "ALL",
            count=len(results),
            successful=sum(1 for r in results if r.success)
        )

        return results

    def execute_trade(self, trade_request: MT5TradeRequest) -> MT5TradeResult:
        """Execute a trade order."""
        if not self.is_connected():
            return MT5TradeResult(
                success=False,
                error_message="Not connected to MT5"
            )

        # Validate symbol
        symbol_info = self.get_symbol_info(trade_request.symbol)
        if not symbol_info:
            return MT5TradeResult(
                success=False,
                error_message=f"Invalid symbol: {trade_request.symbol}"
            )

        # Check if symbol is in allowed list
        if trade_request.symbol not in config.trading.symbols:
            return MT5TradeResult(
                success=False,
                error_message=f"Symbol {trade_request.symbol} not in allowed symbols list"
            )

        # Validate volume
        volume = min(trade_request.volume, config.trading.max_lot_size)
        volume = max(volume, symbol_info['volume_min'])

        # Round volume to valid step
        volume_step = symbol_info['volume_step']
        volume = round(volume / volume_step) * volume_step

        # Get current price if not specified
        if trade_request.price is None:
            prices = self.get_current_price(trade_request.symbol)
            if not prices:
                return MT5TradeResult(
                    success=False,
                    error_message="Failed to get current price"
                )

            if trade_request.action == TradeAction.BUY:
                price = prices['ask']
                order_type = mt5.ORDER_TYPE_BUY
            else:
                price = prices['bid']
                order_type = mt5.ORDER_TYPE_SELL
        else:
            price = trade_request.price
            if trade_request.action == TradeAction.BUY:
                order_type = mt5.ORDER_TYPE_BUY_LIMIT if price < symbol_info['ask'] else mt5.ORDER_TYPE_BUY_STOP
            else:
                order_type = mt5.ORDER_TYPE_SELL_LIMIT if price > symbol_info['bid'] else mt5.ORDER_TYPE_SELL_STOP

        # Prepare order request
        request = {
            "action": mt5.TRADE_ACTION_DEAL,
            "symbol": trade_request.symbol,
            "volume": volume,
            "type": order_type,
            "price": price,
            "sl": trade_request.stop_loss,
            "tp": trade_request.take_profit,
            "magic": trade_request.magic,
            "comment": trade_request.comment or "Webhook trade",
            "type_time": mt5.ORDER_TIME_GTC,
            "type_filling": mt5.ORDER_FILLING_IOC,
        }

        # Remove None values
        request = {k: v for k, v in request.items() if v is not None}

        logger.info("Executing trade", request=request)

        # Send order
        result = mt5.order_send(request)

        if result.retcode != mt5.TRADE_RETCODE_DONE:
            error_msg = f"Trade failed: {result.retcode} - {result.comment}"
            logger.error(error_msg, retcode=result.retcode)
            return MT5TradeResult(
                success=False,
                error_code=result.retcode,
                error_message=error_msg
            )

        logger.info(
            "Trade executed successfully",
            ticket=result.order,
            symbol=trade_request.symbol,
            volume=volume,
            price=result.price
        )

        return MT5TradeResult(
            success=True,
            ticket=result.order,
            order_id=result.order,
            volume=volume,
            price=result.price,
            comment="Trade executed successfully"
        )

    def get_account_info(self) -> Optional[Dict[str, Any]]:
        """Get account information."""
        if not self.is_connected():
            return None

        account = mt5.account_info()
        if account is None:
            return None

        return {
            'login': account.login,
            'server': account.server,
            'name': account.name,
            'company': account.company,
            'currency': account.currency,
            'balance': account.balance,
            'equity': account.equity,
            'margin': account.margin,
            'free_margin': account.margin_free,
            'margin_level': account.margin_level,
            'profit': account.profit
        }

    def validate_symbol(self, symbol: str) -> bool:
        """Validate if symbol is available for trading."""
        if symbol not in config.trading.symbols:
            logger.warning(f"Symbol {symbol} not in allowed symbols list")
            return False

        symbol_info = self.get_symbol_info(symbol)
        if not symbol_info:
            logger.error(f"Symbol {symbol} not found in MT5")
            return False

        return True

    def __enter__(self):
        """Context manager entry."""
        self.connect()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.disconnect()
