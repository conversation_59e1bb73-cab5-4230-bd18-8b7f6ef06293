# Setup Guide - TradingView to MetaTrader 5 Webhook System

## Prerequisites

1. **Windows Server** (tested on Windows Server 2019/2022)
2. **MetaTrader 5** installed at: `C:\Program Files\MetaTrader 5\terminal64.exe`
3. **Python 3.8+** installed
4. **Active MT5 broker account** with login credentials
5. **TradingView account** with alert capabilities

## Installation Steps

### 1. Download and Setup

```powershell
# Navigate to your desired directory
cd C:\Users\<USER>\Documents\tvtomt5

# Install Python dependencies
pip install -r requirements.txt
```

### 2. Configure Environment

```powershell
# Copy example environment file
copy .env.example .env

# Edit .env file with your settings
notepad .env
```

**Required Configuration:**
```env
# MetaTrader 5 Settings (REQUIRED)
MT5_ACCOUNT=********
MT5_PASSWORD=your_mt5_password
MT5_SERVER=YourBroker-Demo

# Webhook Settings (REQUIRED)
WEBHOOK_SECRET_KEY=your_secret_webhook_key_here

# Trading Configuration (OPTIONAL - has defaults)
TRADING_SYMBOLS=["EURUSD", "GBPUSD", "USDJPY", "XAUUSD"]
TRADING_DEFAULT_LOT_SIZE=0.1
TRADING_MAX_LOT_SIZE=1.0

# Trading Schedule (OPTIONAL)
SCHEDULE_ENABLED=true
SCHEDULE_START_TIME=09:00
SCHEDULE_END_TIME=17:00
SCHEDULE_TIMEZONE=UTC
```

### 3. Test Connection

```powershell
# Test MT5 connection
python scripts/test_connection.py
```

Expected output:
```
✅ MT5 connection successful
Account Info: login=********, balance=10000.0, equity=10000.0
Symbol EURUSD: ✅ Valid
Symbol GBPUSD: ✅ Valid
🎉 All tests passed! System is ready for trading.
```

### 4. Start the System

```powershell
# Start webhook server
python main.py

# Or start in standalone mode (no webhook server)
python main.py --mode standalone
```

### 5. Test Webhook Reception

Open a new terminal and run:
```powershell
python scripts/send_test_webhook.py
```

## TradingView Configuration

### 1. Create Alert

1. Open TradingView and go to your chart
2. Click "Alert" button or press Alt+A
3. Configure your alert conditions
4. In the "Notifications" tab, enable "Webhook URL"

### 2. Webhook URL

```
http://YOUR_SERVER_IP:8000/webhook
```

Replace `YOUR_SERVER_IP` with your server's IP address.

### 3. Alert Message Format

Use this JSON format in your TradingView alert message:

**Basic Buy Signal:**
```json
{
    "secret": "your_secret_webhook_key_here",
    "action": "buy",
    "symbol": "EURUSD",
    "quantity": 0.1,
    "stop_loss_pips": 50,
    "take_profit_pips": 100,
    "comment": "TradingView BUY Signal"
}
```

**Basic Sell Signal:**
```json
{
    "secret": "your_secret_webhook_key_here",
    "action": "sell",
    "symbol": "EURUSD",
    "quantity": 0.1,
    "stop_loss_pips": 50,
    "take_profit_pips": 100,
    "comment": "TradingView SELL Signal"
}
```

**Close All Positions:**
```json
{
    "secret": "your_secret_webhook_key_here",
    "action": "close_all",
    "symbol": "EURUSD",
    "comment": "Close all EURUSD positions"
}
```

### 4. Advanced Options

You can also specify exact prices instead of pips:
```json
{
    "secret": "your_secret_webhook_key_here",
    "action": "buy",
    "symbol": "EURUSD",
    "quantity": 0.1,
    "stop_loss": 1.0950,
    "take_profit": 1.1050,
    "comment": "TradingView Signal with exact prices"
}
```

## Firewall Configuration

If running on a server, ensure port 8000 is open:

```powershell
# Open Windows Firewall
netsh advfirewall firewall add rule name="TradingView Webhook" dir=in action=allow protocol=TCP localport=8000
```

## Running as Windows Service

To run as a Windows service, you can use `nssm` (Non-Sucking Service Manager):

1. Download NSSM from https://nssm.cc/download
2. Install the service:

```powershell
nssm install TradingViewMT5 "C:\Python\python.exe" "C:\Users\<USER>\Documents\tvtomt5\main.py"
nssm set TradingViewMT5 AppDirectory "C:\Users\<USER>\Documents\tvtomt5"
nssm start TradingViewMT5
```

## Monitoring and Logs

- **Log File**: `trading_system.log` (rotated automatically)
- **Health Check**: `http://localhost:8000/health`
- **System Status**: `http://localhost:8000/status`

## Troubleshooting

### Common Issues

1. **MT5 Connection Failed**
   - Verify MT5 is installed and running
   - Check account credentials in .env
   - Ensure MT5 allows automated trading

2. **Symbol Not Found**
   - Check if symbol exists in your broker
   - Verify symbol name format (e.g., "EURUSD" vs "EUR/USD")

3. **Webhook Not Received**
   - Check firewall settings
   - Verify webhook URL is accessible
   - Check secret key matches

4. **Trading Outside Hours**
   - Check SCHEDULE_ENABLED setting
   - Verify SCHEDULE_START_TIME and SCHEDULE_END_TIME
   - Check timezone configuration

### Debug Mode

Run with debug logging:
```powershell
set LOG_LEVEL=DEBUG
python main.py
```
