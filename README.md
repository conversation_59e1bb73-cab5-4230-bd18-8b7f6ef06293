# TradingView to MetaTrader 5 Webhook Trading System

A complete Python-based trading system that connects TradingView alerts to MetaTrader 5 via webhooks for automated trading on Windows Server.

## Features

- ✅ **Webhook Alert Reception**: Receive trading alerts from TradingView via HTTP webhooks
- ✅ **Direct MT5 Integration**: Direct connection to MetaTrader 5 broker
- ✅ **Automatic Position Management**: Automatically close existing positions before opening new ones
- ✅ **Stop Loss & Take Profit**: Automatic SL/TP calculation and setting
- ✅ **Symbol Configuration**: Configurable list of allowed trading symbols
- ✅ **Time-based Trading**: Trade only during specified time windows
- ✅ **Security**: Webhook authentication with secret keys
- ✅ **Comprehensive Logging**: Detailed logging with rotation and colors
- ✅ **Error Handling**: Robust error handling and recovery
- ✅ **Health Monitoring**: Health check endpoints and system status

## Quick Start

### 1. Installation

```bash
# Clone or download this project
cd tvtomt5

# Install dependencies
pip install -r requirements.txt
```

### 2. Configuration

Copy the example environment file and configure your settings:

```bash
copy .env.example .env
```

Edit `.env` with your MetaTrader 5 credentials and preferences:

```env
# MetaTrader 5 Settings
MT5_ACCOUNT=********
MT5_PASSWORD=your_mt5_password
MT5_SERVER=YourBroker-Demo

# Webhook Settings
WEBHOOK_SECRET_KEY=your_secret_webhook_key_here

# Trading Configuration
TRADING_SYMBOLS=["EURUSD", "GBPUSD", "USDJPY"]
TRADING_DEFAULT_LOT_SIZE=0.1

# Trading Schedule (optional)
SCHEDULE_ENABLED=true
SCHEDULE_START_TIME=09:00
SCHEDULE_END_TIME=17:00
```

### 3. Run the System

```bash
# Start the webhook server
python main.py

# Or run in standalone mode (no webhook server)
python main.py --mode standalone
```

The webhook server will start on `http://localhost:8000`

### 4. TradingView Setup

In TradingView, create an alert with webhook URL:
```
http://your-server-ip:8000/webhook
```

Use this JSON format in your alert message:
```json
{
    "secret": "your_secret_webhook_key_here",
    "action": "buy",
    "symbol": "EURUSD",
    "quantity": 0.1,
    "stop_loss_pips": 50,
    "take_profit_pips": 100,
    "comment": "TradingView Signal"
}
```

## API Endpoints

- `GET /` - Root endpoint (health check)
- `GET /health` - Detailed health status
- `GET /status` - System status with positions and account info
- `POST /webhook` - Receive TradingView alerts
- `POST /close-all?symbol=EURUSD` - Close all positions (optional symbol filter)

## Webhook Alert Format

```json
{
    "secret": "your_secret_key",
    "action": "buy|sell|close|close_all",
    "symbol": "EURUSD",
    "quantity": 0.1,
    "stop_loss": 1.0950,
    "take_profit": 1.1050,
    "stop_loss_pips": 50,
    "take_profit_pips": 100,
    "comment": "Optional comment"
}
```

## Trading Logic

1. **Signal Reception**: Webhook receives TradingView alert
2. **Schedule Check**: Verify trading is allowed (time window + trading days)
3. **Position Closure**: Close any existing positions for the symbol
4. **Trade Execution**: Open new position with calculated SL/TP
5. **Logging**: Record all operations with detailed logs

## Configuration Options

### Trading Schedule
- Enable/disable time-based trading
- Set start/end times (24-hour format)
- Configure trading days (Monday=0, Sunday=6)
- Timezone support

### Risk Management
- Maximum lot size limits
- Default stop loss/take profit in pips
- Symbol whitelist
- Magic number for trade identification

### Logging
- Configurable log levels
- File rotation with size limits
- Colored console output
- Structured logging with timestamps

## Security

- Webhook secret key authentication
- Optional signature verification
- Symbol whitelist validation
- Input sanitization and validation

## Requirements

- Windows Server (tested on Windows Server 2019/2022)
- MetaTrader 5 installed and configured
- Python 3.8+
- Active MT5 broker account

## Support

For issues or questions, check the logs in `trading_system.log` for detailed error information.
