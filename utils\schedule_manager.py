"""Trading schedule management utilities."""

from datetime import datetime, time
from typing import List
import pytz

from config import config
from utils.logger import get_logger

logger = get_logger(__name__)


class ScheduleManager:
    """Manages trading schedule and time-based restrictions."""
    
    def __init__(self):
        self.enabled = config.schedule.enabled
        self.start_time = self._parse_time(config.schedule.start_time)
        self.end_time = self._parse_time(config.schedule.end_time)
        self.timezone = pytz.timezone(config.schedule.timezone)
        self.trading_days = config.schedule.trading_days
        
        logger.info(
            "Schedule manager initialized",
            enabled=self.enabled,
            start_time=self.start_time,
            end_time=self.end_time,
            timezone=config.schedule.timezone,
            trading_days=self.trading_days
        )
    
    def _parse_time(self, time_str: str) -> time:
        """Parse time string in HH:MM format."""
        try:
            hour, minute = map(int, time_str.split(':'))
            return time(hour, minute)
        except ValueError:
            logger.error(f"Invalid time format: {time_str}. Expected HH:MM")
            raise ValueError(f"Invalid time format: {time_str}")
    
    def is_trading_allowed(self, check_time: datetime = None) -> bool:
        """Check if trading is allowed at the given time."""
        if not self.enabled:
            return True
        
        if check_time is None:
            check_time = datetime.now(self.timezone)
        elif check_time.tzinfo is None:
            check_time = self.timezone.localize(check_time)
        else:
            check_time = check_time.astimezone(self.timezone)
        
        # Check if it's a trading day
        if check_time.weekday() not in self.trading_days:
            logger.debug(
                "Trading not allowed - non-trading day",
                weekday=check_time.weekday(),
                trading_days=self.trading_days
            )
            return False
        
        # Check if it's within trading hours
        current_time = check_time.time()
        
        if self.start_time <= self.end_time:
            # Same day trading (e.g., 09:00 to 17:00)
            in_trading_hours = self.start_time <= current_time <= self.end_time
        else:
            # Overnight trading (e.g., 22:00 to 06:00)
            in_trading_hours = current_time >= self.start_time or current_time <= self.end_time
        
        if not in_trading_hours:
            logger.debug(
                "Trading not allowed - outside trading hours",
                current_time=current_time,
                start_time=self.start_time,
                end_time=self.end_time
            )
            return False
        
        return True
    
    def get_next_trading_session(self, from_time: datetime = None) -> datetime:
        """Get the next trading session start time."""
        if not self.enabled:
            return datetime.now(self.timezone)
        
        if from_time is None:
            from_time = datetime.now(self.timezone)
        elif from_time.tzinfo is None:
            from_time = self.timezone.localize(from_time)
        else:
            from_time = from_time.astimezone(self.timezone)
        
        # Start from the next day if we're past trading hours today
        check_date = from_time.date()
        if from_time.time() > self.end_time:
            check_date = check_date.replace(day=check_date.day + 1)
        
        # Find next trading day
        for i in range(7):  # Check up to 7 days ahead
            check_datetime = datetime.combine(check_date, self.start_time)
            check_datetime = self.timezone.localize(check_datetime)
            
            if check_datetime.weekday() in self.trading_days:
                return check_datetime
            
            check_date = check_date.replace(day=check_date.day + 1)
        
        # Fallback to current time if no trading day found
        return from_time
    
    def get_trading_status_message(self) -> str:
        """Get current trading status message."""
        if not self.enabled:
            return "Trading schedule disabled - trading allowed 24/7"
        
        now = datetime.now(self.timezone)
        is_allowed = self.is_trading_allowed(now)
        
        if is_allowed:
            return f"✅ Trading ACTIVE (until {self.end_time})"
        else:
            next_session = self.get_next_trading_session(now)
            return f"🚫 Trading INACTIVE (next session: {next_session.strftime('%Y-%m-%d %H:%M %Z')})"
