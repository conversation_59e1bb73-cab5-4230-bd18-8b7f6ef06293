# Docker Compose for TradingView to MT5 System
# Note: This is for development/testing only
# Production deployment should be on Windows Server with MT5 installed

version: '3.8'

services:
  trading-system:
    build: .
    ports:
      - "8000:8000"
    environment:
      - WEBHOOK_HOST=0.0.0.0
      - WEBHOOK_PORT=8000
      - LOG_LEVEL=INFO
    env_file:
      - .env
    volumes:
      - ./logs:/app/logs
      - ./.env:/app/.env
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: Add a reverse proxy for production
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - trading-system
    restart: unless-stopped
    profiles:
      - production
